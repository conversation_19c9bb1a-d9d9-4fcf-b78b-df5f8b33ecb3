<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    redirect('../auth/login.php');
}

$page_title = 'System Logs';

// Define page actions
$page_actions = [
    [
        'url' => 'index.php',
        'label' => 'Back to Dashboard',
        'icon' => 'fas fa-arrow-left'
    ]
];

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Logs</li>
    </ol>
</nav>

<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-file-alt me-2"></i>
                    System Logs
                </h3>
            </div>
            <div class="card-body">
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-file-alt" style="font-size: 4rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">System Logs</p>
                    <p class="empty-subtitle text-muted">
                        This page will contain system logging functionality including:
                    </p>
                    <ul class="text-start" style="max-width: 400px; margin: 0 auto;">
                        <li>View system activity logs</li>
                        <li>User login/logout history</li>
                        <li>Transaction and transfer logs</li>
                        <li>Security events and alerts</li>
                        <li>Error logs and debugging info</li>
                    </ul>
                    <div class="empty-action mt-4">
                        <a href="users.php" class="btn btn-primary">
                            <i class="fas fa-users me-2"></i>
                            Manage Users Instead
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>

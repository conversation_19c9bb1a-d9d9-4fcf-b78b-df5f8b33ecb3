<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Crypto Operations';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        $wallet_id = intval($_POST['wallet_id']);
        $operation_type = $_POST['operation_type'];
        $amount = floatval($_POST['amount']);
        $description = trim($_POST['description']);
        $operation_date = $_POST['operation_date'] ?? date('Y-m-d H:i:s');
        
        // Validate inputs
        if (empty($wallet_id) || empty($operation_type) || $amount <= 0 || empty($description)) {
            throw new Exception("All fields are required and amount must be positive.");
        }
        
        // Get wallet details
        $wallet_query = "SELECT cw.*, a.first_name, a.last_name, a.username 
                        FROM crypto_wallets cw 
                        LEFT JOIN accounts a ON cw.account_id = a.id 
                        WHERE cw.wallet_id = ?";
        $wallet_result = $db->query($wallet_query, [$wallet_id]);
        $wallet = $wallet_result->fetch_assoc();
        
        if (!$wallet) {
            throw new Exception("Wallet not found.");
        }
        
        // Calculate new balance
        $current_balance = floatval($wallet['wallet_balance']);
        $new_balance = $current_balance;
        
        if ($operation_type === 'credit') {
            $new_balance = $current_balance + $amount;
        } elseif ($operation_type === 'debit') {
            if ($current_balance < $amount) {
                throw new Exception("Insufficient balance. Current balance: " . number_format($current_balance, 8) . " " . $wallet['cryptocurrency']);
            }
            $new_balance = $current_balance - $amount;
        }
        
        // Generate transaction hash
        $transaction_hash = bin2hex(random_bytes(32));
        
        // Insert transaction record
        $insert_transaction = "INSERT INTO crypto_transactions (
            wallet_id, account_id, transaction_type, amount, cryptocurrency, description, 
            transaction_hash, status, processed_by, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'confirmed', ?, ?)";
        
        $transaction_id = $db->insert($insert_transaction, [
            $wallet_id, $wallet['account_id'], $operation_type, $amount, $wallet['cryptocurrency'],
            $description, $transaction_hash, $_SESSION['user_id'], $operation_date
        ]);
        
        if ($transaction_id) {
            // Update wallet balance
            $update_balance = "UPDATE crypto_wallets SET wallet_balance = ?, updated_at = NOW() WHERE wallet_id = ?";
            $db->query($update_balance, [$new_balance, $wallet_id]);
            
            $success = "✅ " . ucfirst($operation_type) . " operation completed successfully! " .
                      "Amount: " . number_format($amount, 8) . " " . $wallet['cryptocurrency'] . 
                      " | New Balance: " . number_format($new_balance, 8) . " " . $wallet['cryptocurrency'];
        } else {
            throw new Exception("Failed to record transaction.");
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get all active crypto wallets for the dropdown
try {
    $db = getDB();
    $wallets_query = "SELECT cw.wallet_id, cw.wallet_name, cw.wallet_address, cw.cryptocurrency, cw.wallet_balance,
                      a.first_name, a.last_name, a.username
                      FROM crypto_wallets cw 
                      LEFT JOIN accounts a ON cw.account_id = a.id 
                      WHERE cw.status = 'active'
                      ORDER BY a.first_name, a.last_name, cw.cryptocurrency";
    $wallets_result = $db->query($wallets_query);
    $wallets = [];
    while ($row = $wallets_result->fetch_assoc()) {
        $wallets[] = $row;
    }
} catch (Exception $e) {
    $wallets = [];
    $error = "Failed to load wallets: " . $e->getMessage();
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="crypto-wallets.php">Crypto Wallets</a></li>
        <li class="breadcrumb-item active" aria-current="page">Crypto Operations</li>
    </ol>
</nav>

<!-- Success Message -->
<?php if (isset($success)): ?>
<div class="alert alert-success alert-dismissible" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo $success; ?></div>
        </div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger alert-dismissible" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<div class="row">
    <!-- Operation Form -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-exchange-alt me-2"></i>
                    Cryptocurrency Operations
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Select Wallet <span class="text-danger">*</span></label>
                                <select name="wallet_id" class="form-select" required onchange="updateWalletInfo(this)">
                                    <option value="">Choose a crypto wallet...</option>
                                    <?php foreach ($wallets as $wallet): ?>
                                    <option value="<?php echo $wallet['wallet_id']; ?>" 
                                            data-balance="<?php echo $wallet['wallet_balance']; ?>"
                                            data-crypto="<?php echo $wallet['cryptocurrency']; ?>"
                                            data-address="<?php echo $wallet['wallet_address']; ?>"
                                            data-owner="<?php echo htmlspecialchars($wallet['first_name'] . ' ' . $wallet['last_name']); ?>">
                                        <?php echo htmlspecialchars($wallet['wallet_name']); ?> 
                                        (<?php echo $wallet['cryptocurrency']; ?>) - 
                                        <?php echo htmlspecialchars($wallet['first_name'] . ' ' . $wallet['last_name']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Operation Type <span class="text-danger">*</span></label>
                                <select name="operation_type" class="form-select" required>
                                    <option value="">Select operation...</option>
                                    <option value="credit" <?php echo ($_POST['operation_type'] ?? '') === 'credit' ? 'selected' : ''; ?>>Credit (Add Funds)</option>
                                    <option value="debit" <?php echo ($_POST['operation_type'] ?? '') === 'debit' ? 'selected' : ''; ?>>Debit (Remove Funds)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Amount <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" name="amount" class="form-control" 
                                           step="0.00000001" min="0.00000001" max="1000000000" 
                                           placeholder="0.00000000" 
                                           value="<?php echo $_POST['amount'] ?? ''; ?>" required>
                                    <span class="input-group-text" id="crypto-symbol">CRYPTO</span>
                                </div>
                                <small class="form-hint">Maximum amount: 1 billion units</small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Operation Date</label>
                                <input type="datetime-local" name="operation_date" class="form-control" 
                                       value="<?php echo $_POST['operation_date'] ?? date('Y-m-d\TH:i'); ?>">
                                <small class="form-hint">Leave blank for current date/time</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Description <span class="text-danger">*</span></label>
                        <textarea name="description" class="form-control" rows="3" 
                                  placeholder="Enter operation description..." required><?php echo $_POST['description'] ?? ''; ?></textarea>
                        <small class="form-hint">Provide details about this crypto operation</small>
                    </div>
                    
                    <!-- Wallet Info Display -->
                    <div id="wallet-info" class="alert alert-info" style="display: none;">
                        <h5>Selected Wallet Information:</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>Owner:</strong> <span id="wallet-owner">-</span><br>
                                <strong>Cryptocurrency:</strong> <span id="wallet-crypto">-</span><br>
                            </div>
                            <div class="col-md-6">
                                <strong>Current Balance:</strong> <span id="wallet-balance">-</span><br>
                                <strong>Address:</strong> <span id="wallet-address" class="font-monospace">-</span><br>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-check me-2"></i>
                            Execute Operation
                        </button>
                        <a href="crypto-wallets.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Wallets
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Guidelines -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Operation Guidelines
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h4 class="alert-title">Credit Operations</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li>Add cryptocurrency to wallet</li>
                        <li>No balance limit restrictions</li>
                        <li>Instant confirmation</li>
                        <li>Creates positive transaction</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h4 class="alert-title">Debit Operations</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li>Remove cryptocurrency from wallet</li>
                        <li>Requires sufficient balance</li>
                        <li>Cannot exceed current balance</li>
                        <li>Creates negative transaction</li>
                    </ul>
                </div>
                
                <div class="alert alert-success">
                    <h4 class="alert-title">Security Features</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li>All operations are logged</li>
                        <li>Blockchain-style transaction hashes</li>
                        <li>Admin approval tracking</li>
                        <li>Real-time balance updates</li>
                        <li>Audit trail maintained</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateWalletInfo(select) {
    const option = select.options[select.selectedIndex];
    const walletInfo = document.getElementById('wallet-info');
    const cryptoSymbol = document.getElementById('crypto-symbol');
    
    if (option.value) {
        const balance = parseFloat(option.dataset.balance);
        const crypto = option.dataset.crypto;
        const address = option.dataset.address;
        const owner = option.dataset.owner;
        
        document.getElementById('wallet-owner').textContent = owner;
        document.getElementById('wallet-crypto').textContent = crypto;
        document.getElementById('wallet-balance').textContent = balance.toFixed(8) + ' ' + crypto;
        document.getElementById('wallet-address').textContent = address.substring(0, 8) + '...' + address.substring(address.length - 8);
        
        cryptoSymbol.textContent = crypto;
        walletInfo.style.display = 'block';
    } else {
        walletInfo.style.display = 'none';
        cryptoSymbol.textContent = 'CRYPTO';
    }
}
</script>

<?php include 'includes/admin-footer.php'; ?>

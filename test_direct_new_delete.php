<?php
/**
 * Direct test for delete user functionality
 */

require_once 'config/config.php';

echo "<h2>🧪 Direct Delete User Test</h2>";
echo "<hr>";

// Set test user ID
$test_user_id = 9; // ajax_test_user

echo "<h3>Testing Deletion of User ID: $test_user_id</h3>";

// Simulate admin session
session_start();
$_SESSION['user_id'] = 1; // Admin user
$_SESSION['is_admin'] = true;

echo "✅ Admin session initialized<br>";

// Get user details before deletion
try {
    $db = getDB();
    $user_query = "SELECT id, username, email, first_name, last_name, account_number FROM accounts WHERE id = ?";
    $user_result = $db->query($user_query, [$test_user_id]);
    
    if ($user_result->num_rows > 0) {
        $user = $user_result->fetch_assoc();
        echo "<h4>📋 User to Delete:</h4>";
        echo "<ul>";
        echo "<li><strong>ID:</strong> {$user['id']}</li>";
        echo "<li><strong>Username:</strong> {$user['username']}</li>";
        echo "<li><strong>Email:</strong> {$user['email']}</li>";
        echo "<li><strong>Name:</strong> {$user['first_name']} {$user['last_name']}</li>";
        echo "<li><strong>Account Number:</strong> {$user['account_number']}</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ User with ID $test_user_id not found.</p>";
        exit;
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error getting user details: " . $e->getMessage() . "</p>";
    exit;
}

// Simulate the AJAX request
$_SERVER['REQUEST_METHOD'] = 'POST';
$_GET['id'] = $test_user_id;
$_GET['ajax'] = '1';

echo "<h4>🚀 Executing Delete Request...</h4>";

// Change to admin directory
$original_dir = getcwd();
chdir(__DIR__ . '/admin');

// Capture output
ob_start();
$error_occurred = false;

try {
    include 'delete-user-new.php';
} catch (Exception $e) {
    $error_occurred = true;
    $error_message = $e->getMessage();
} catch (Error $e) {
    $error_occurred = true;
    $error_message = $e->getMessage();
}

$response = ob_get_contents();
ob_end_clean();

// Change back to original directory
chdir($original_dir);

echo "<h4>📥 Delete Response:</h4>";

if ($error_occurred) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h5>❌ PHP Error Occurred:</h5>";
    echo "<p>" . htmlspecialchars($error_message) . "</p>";
    echo "</div>";
}

echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6; max-height: 400px; overflow: auto;'>";
echo htmlspecialchars($response);
echo "</pre>";

// Try to parse JSON response
$json_response = json_decode($response, true);
if ($json_response) {
    echo "<h4>📊 Parsed JSON Response:</h4>";
    $bg_color = $json_response['success'] ? '#d4edda' : '#f8d7da';
    $icon = $json_response['success'] ? '✅' : '❌';
    
    echo "<div style='background: $bg_color; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h5>$icon " . ($json_response['success'] ? 'Success' : 'Failed') . "</h5>";
    echo "<p><strong>Message:</strong> " . htmlspecialchars($json_response['message']) . "</p>";
    
    if (isset($json_response['data'])) {
        echo "<p><strong>Data:</strong></p>";
        echo "<pre>" . htmlspecialchars(json_encode($json_response['data'], JSON_PRETTY_PRINT)) . "</pre>";
    }
    echo "</div>";
    
    if ($json_response['success']) {
        echo "<h4>🔍 Verification - Checking if user is really deleted:</h4>";
        
        try {
            $verify_query = "SELECT id, username FROM accounts WHERE id = ?";
            $verify_result = $db->query($verify_query, [$test_user_id]);
            
            if ($verify_result->num_rows === 0) {
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h5>✅ Verification Successful</h5>";
                echo "<p>User with ID $test_user_id has been completely removed from the database.</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                echo "<h5>❌ Verification Failed</h5>";
                echo "<p>User with ID $test_user_id still exists in the database!</p>";
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h5>⚠️ Verification Error</h5>";
            echo "<p>Could not verify deletion: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
        
        echo "<h4>📊 Current Users After Deletion:</h4>";
        try {
            $all_users = $db->query("SELECT id, username, email, is_admin FROM accounts ORDER BY id");
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr style='background: #f2f2f2;'><th>ID</th><th>Username</th><th>Email</th><th>Admin</th></tr>";
            
            while ($row = $all_users->fetch_assoc()) {
                $highlight = ($row['id'] == $test_user_id) ? "style='background: #f8d7da;'" : "";
                echo "<tr $highlight>";
                echo "<td>{$row['id']}</td>";
                echo "<td>{$row['username']}</td>";
                echo "<td>{$row['email']}</td>";
                echo "<td>" . ($row['is_admin'] ? 'Yes' : 'No') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } catch (Exception $e) {
            echo "<p>Error showing users: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h5>❌ Invalid Response</h5>";
    echo "<p>The response is not valid JSON. Check for PHP errors or output before the JSON.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🎯 Test Summary</h4>";
echo "<p>This test simulates the exact same request that would be made by the admin interface when deleting a user via AJAX.</p>";
echo "<p>If successful, the user should be completely removed from the database along with all related records.</p>";
echo "</div>";
?>

<?php
require_once 'config/config.php';
require_once 'config/database.php';

// Initialize database connection
$db = Database::getInstance();

echo "<h2>Database Tables Check</h2><hr>";

try {
    // Get all tables in the database
    $query = "SHOW TABLES";
    $result = $db->query($query);
    
    echo "<h3>Existing Tables:</h3>";
    echo "<ul>";
    
    if ($result) {
        while ($row = $result->fetch_array()) {
            $tableName = $row[0];
            echo "<li><strong>$tableName</strong>";
            
            // Get record count for each table
            try {
                $countQuery = "SELECT COUNT(*) as count FROM `$tableName`";
                $countResult = $db->query($countQuery);
                if ($countResult) {
                    $countRow = $countResult->fetch_assoc();
                    echo " (Records: " . $countRow['count'] . ")";
                }
            } catch (Exception $e) {
                echo " (Count error: " . $e->getMessage() . ")";
            }
            
            echo "</li>";
        }
    }
    
    echo "</ul>";
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage();
}
?>

<?php
require_once '../../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    redirect('../login.php');
}

$page_title = 'Account Management';

$errors = [];
$success = '';

// Handle credit/debit operations
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $user_id = intval($_POST['user_id'] ?? 0);
    $amount = floatval($_POST['amount'] ?? 0);
    $currency = sanitizeInput($_POST['currency'] ?? 'USD');
    $description = sanitizeInput($_POST['description'] ?? '');
    $category = sanitizeInput($_POST['category'] ?? 'adjustment');
    
    if ($action === 'credit' || $action === 'debit') {
        if ($user_id <= 0) {
            $errors[] = 'Please select a valid user.';
        }
        
        if ($amount <= 0) {
            $errors[] = 'Amount must be greater than zero.';
        }
        
        if (empty($description)) {
            $errors[] = 'Description is required.';
        }
        
        if (empty($errors)) {
            try {
                $db = getDB();
                $db->beginTransaction();
                
                // Get user account
                $user_result = $db->query("SELECT id, first_name, last_name, balance FROM accounts WHERE id = ? AND is_admin = 0", [$user_id]);
                
                if ($user_result->num_rows === 0) {
                    $errors[] = 'User not found.';
                } else {
                    $user = $user_result->fetch_assoc();
                    $old_balance = $user['balance'];
                    
                    // Calculate new balance
                    if ($action === 'credit') {
                        $new_balance = $old_balance + $amount;
                    } else {
                        $new_balance = $old_balance - $amount;
                        if ($new_balance < 0) {
                            $errors[] = 'Insufficient funds for debit operation.';
                        }
                    }
                    
                    if (empty($errors)) {
                        // Update account balance
                        $db->query("UPDATE accounts SET balance = ? WHERE id = ?", [$new_balance, $user_id]);
                        
                        // Generate reference number
                        $reference = 'ADM' . date('Ymd') . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
                        
                        // Record transaction
                        $transaction_sql = "INSERT INTO account_transactions (
                                              account_id, transaction_type, amount, currency, description, 
                                              reference_number, category, processed_by
                                            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                        
                        $db->query($transaction_sql, [
                            $user_id, $action, $amount, $currency, $description,
                            $reference, $category, $_SESSION['user_id']
                        ]);
                        
                        // Log activity
                        logActivity($_SESSION['user_id'], "Admin {$action}ed account", 'accounts', $user_id, 
                                   ['balance' => $old_balance], ['balance' => $new_balance]);
                        
                        $db->commit();
                        
                        $success = ucfirst($action) . " of " . formatCurrency($amount) . " successful! Reference: $reference";
                    }
                }
                
            } catch (Exception $e) {
                $db->rollback();
                error_log("Account operation error: " . $e->getMessage());
                $errors[] = 'Operation failed. Please try again.';
            }
        }
    }
}

// Get users for dropdown
try {
    $db = getDB();
    $users_result = $db->query("SELECT id, first_name, last_name, username, account_number, balance FROM accounts WHERE is_admin = 0 ORDER BY first_name, last_name");
    
    // Get recent transactions
    $recent_transactions = $db->query("
        SELECT at.*, a.first_name, a.last_name, a.account_number,
               admin.first_name as admin_first_name, admin.last_name as admin_last_name
        FROM account_transactions at
        LEFT JOIN accounts a ON at.account_id = a.id
        LEFT JOIN accounts admin ON at.processed_by = admin.id
        ORDER BY at.created_at DESC
        LIMIT 20
    ");
    
} catch (Exception $e) {
    error_log("Account management fetch error: " . $e->getMessage());
    $users_result = null;
    $recent_transactions = null;
}

include '../../includes/admin_header.php';
?>

<link rel="stylesheet" href="../../assets/admin/admin.css">
<link rel="stylesheet" href="style.css">



<div class="page-wrapper">
    <!-- Page header -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="../index.php">Admin</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Account Management</li>
                        </ol>
                    </nav>
                    <div class="page-pretitle">
                        Administration
                    </div>
                    <h2 class="page-title">
                        Account Management
                    </h2>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Page body -->
    <div class="page-body">
        <div class="container-xl">
            <?php if (!empty($errors)): ?>
            <div class="alert alert-danger" role="alert">
                <div class="d-flex">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <circle cx="12" cy="12" r="9"/>
                            <line x1="15" y1="9" x2="9" y2="15"/>
                            <line x1="9" y1="9" x2="15" y2="15"/>
                        </svg>
                    </div>
                    <div>
                        <h4 class="alert-title">Error!</h4>
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <?php if (!empty($success)): ?>
            <div class="alert alert-success" role="alert">
                <div class="d-flex">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M5 12l5 5l10 -10"/>
                        </svg>
                    </div>
                    <div>
                        <h4 class="alert-title">Success!</h4>
                        <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <div class="row row-cards">
                <!-- Credit/Debit Operations -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <path d="M17 8v-3a1 1 0 0 0 -1 -1h-10a2 2 0 0 0 0 4h12a1 1 0 0 1 1 1v3m0 4v3a1 1 0 0 1 -1 1h-12a2 2 0 0 1 -2 -2v-12"/>
                                    <path d="M20 12v4h-4a2 2 0 0 1 0 -4h4"/>
                                </svg>
                                Credit/Debit Account
                            </h3>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="" id="account-operation-form">
                                <div class="mb-3">
                                    <label class="form-label required">Select User</label>
                                    <select name="user_id" class="form-select" required id="user-select">
                                        <option value="">Choose a user...</option>
                                        <?php if ($users_result): ?>
                                        <?php while ($user = $users_result->fetch_assoc()): ?>
                                        <option value="<?php echo $user['id']; ?>" data-balance="<?php echo $user['balance']; ?>">
                                            <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?> 
                                            (@<?php echo htmlspecialchars($user['username']); ?>) - 
                                            <?php echo formatCurrency($user['balance']); ?>
                                        </option>
                                        <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">Operation</label>
                                            <div class="form-selectgroup">
                                                <label class="form-selectgroup-item">
                                                    <input type="radio" name="action" value="credit" class="form-selectgroup-input" required>
                                                    <span class="form-selectgroup-label">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-1 text-green" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                            <polyline points="17 11 12 6 7 11"/>
                                                            <polyline points="12 6 12 18"/>
                                                        </svg>
                                                        Credit
                                                    </span>
                                                </label>
                                                <label class="form-selectgroup-item">
                                                    <input type="radio" name="action" value="debit" class="form-selectgroup-input" required>
                                                    <span class="form-selectgroup-label">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon me-1 text-red" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                            <polyline points="7 13 12 18 17 13"/>
                                                            <polyline points="12 18 12 6"/>
                                                        </svg>
                                                        Debit
                                                    </span>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label required">Currency</label>
                                            <select name="currency" class="form-select">
                                                <option value="USD">USD - US Dollar</option>
                                                <option value="EUR">EUR - Euro</option>
                                                <option value="GBP">GBP - British Pound</option>
                                                <option value="CAD">CAD - Canadian Dollar</option>
                                                <option value="AUD">AUD - Australian Dollar</option>
                                                <option value="JPY">JPY - Japanese Yen</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label required">Amount</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" name="amount" class="form-control" step="0.01" min="0.01" placeholder="0.00" required>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label required">Category</label>
                                    <select name="category" class="form-select">
                                        <option value="adjustment">Manual Adjustment</option>
                                        <option value="deposit">Deposit</option>
                                        <option value="withdrawal">Withdrawal</option>
                                        <option value="fee">Fee</option>
                                        <option value="interest">Interest</option>
                                        <option value="virtual_card">Virtual Card</option>
                                        <option value="crypto">Cryptocurrency</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label required">Description</label>
                                    <textarea name="description" class="form-control" rows="3" placeholder="Enter transaction description..." required></textarea>
                                </div>
                                
                                <div class="form-footer">
                                    <button type="submit" class="btn btn-primary" id="submit-btn">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M5 12l5 5l10 -10"/>
                                        </svg>
                                        Process Transaction
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Account Summary -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <path d="M9 11h6l3 -3v12l-3 -3h-6l-3 3v-12l3 3"/>
                                    <path d="M15 15v.01"/>
                                    <path d="M15 12v.01"/>
                                    <path d="M15 9v.01"/>
                                </svg>
                                Selected Account Info
                            </h3>
                        </div>
                        <div class="card-body" id="account-info">
                            <div class="empty">
                                <div class="empty-img">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik02NCA2NlY1NCIgc3Ryb2tlPSIjREFEREUyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cGF0aCBkPSJNNjQgNzhWNzYiIHN0cm9rZT0iI0RBRERFRSI+PHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" alt="" height="128" width="128">
                                </div>
                                <p class="empty-title">Select a user</p>
                                <p class="empty-subtitle text-muted">
                                    Choose a user from the dropdown to view account information.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Transactions -->
            <div class="row row-cards mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Recent Account Transactions</h3>
                        </div>
                        <div class="card-body p-0">
                            <?php if ($recent_transactions && $recent_transactions->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-vcenter card-table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Account</th>
                                            <th>Type</th>
                                            <th>Amount</th>
                                            <th>Category</th>
                                            <th>Description</th>
                                            <th>Processed By</th>
                                            <th>Reference</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($transaction = $recent_transactions->fetch_assoc()): ?>
                                        <tr>
                                            <td class="text-muted">
                                                <?php echo formatDate($transaction['created_at'], 'M d, Y H:i'); ?>
                                            </td>
                                            <td>
                                                <div>
                                                    <div class="font-weight-medium"><?php echo htmlspecialchars($transaction['first_name'] . ' ' . $transaction['last_name']); ?></div>
                                                    <div class="text-muted"><?php echo htmlspecialchars($transaction['account_number']); ?></div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $transaction['transaction_type'] === 'credit' ? 'green' : 'red'; ?>-lt">
                                                    <?php echo ucfirst($transaction['transaction_type']); ?>
                                                </span>
                                            </td>
                                            <td class="text-<?php echo $transaction['transaction_type'] === 'credit' ? 'green' : 'red'; ?>">
                                                <?php echo ($transaction['transaction_type'] === 'credit' ? '+' : '-') . formatCurrency($transaction['amount']); ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-blue-lt"><?php echo ucfirst(str_replace('_', ' ', $transaction['category'])); ?></span>
                                            </td>
                                            <td><?php echo htmlspecialchars($transaction['description']); ?></td>
                                            <td class="text-muted">
                                                <?php echo htmlspecialchars($transaction['admin_first_name'] . ' ' . $transaction['admin_last_name']); ?>
                                            </td>
                                            <td class="text-muted">
                                                <?php echo htmlspecialchars($transaction['reference_number']); ?>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="empty">
                                <div class="empty-img">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik02NCA2NlY1NCIgc3Ryb2tlPSIjREFEREUyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cGF0aCBkPSJNNjQgNzhWNzYiIHN0cm9rZT0iI0RBRERFRSI+PHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" alt="" height="128" width="128">
                                </div>
                                <p class="empty-title">No transactions yet</p>
                                <p class="empty-subtitle text-muted">
                                    Account transactions will appear here.
                                </p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../../assets/admin/admin.js"></script>
<script src="script.js"></script>

<?php include '../../includes/footer.php'; ?>

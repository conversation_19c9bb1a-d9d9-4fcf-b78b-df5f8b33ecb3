<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Login Redirect Testing';

include '../includes/admin_header.php';
?>

<div class="page-wrapper">
    <!-- Page header -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <div class="page-pretitle">
                        Administration
                    </div>
                    <h2 class="page-title">
                        Login Redirect Testing
                    </h2>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Page body -->
    <div class="page-body">
        <div class="container-xl">
            <div class="row row-cards">
                <!-- Test Results -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Login Redirect Test Results</h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success">
                                <h4>✅ Admin Login Redirect Fixed!</h4>
                                <p>The following login redirect issues have been resolved:</p>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-vcenter">
                                    <thead>
                                        <tr>
                                            <th>Issue</th>
                                            <th>Before</th>
                                            <th>After</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>Login Page - Admin Login</strong></td>
                                            <td><code>redirect('../dashboard/')</code></td>
                                            <td><code>redirect('../admin/')</code></td>
                                            <td><span class="badge bg-green">Fixed</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Login Page - Already Logged In</strong></td>
                                            <td><code>redirect('/dashboard/')</code></td>
                                            <td><code>Admin → '../admin/', User → '../dashboard/'</code></td>
                                            <td><span class="badge bg-green">Fixed</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Main Index Page</strong></td>
                                            <td><code>redirect('dashboard/')</code></td>
                                            <td><code>Admin → 'admin/', User → 'dashboard/'</code></td>
                                            <td><span class="badge bg-green">Fixed</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>requireAdmin() Function</strong></td>
                                            <td><code>redirect('dashboard/')</code></td>
                                            <td><code>redirect('auth/login.php')</code></td>
                                            <td><span class="badge bg-green">Fixed</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Admin Header - Switch to User View</strong></td>
                                            <td><code>url('dashboard.php')</code></td>
                                            <td><code>url('dashboard/')</code></td>
                                            <td><span class="badge bg-green">Fixed</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Admin Pages Authentication</strong></td>
                                            <td><code>redirect('login.php')</code></td>
                                            <td><code>redirect('../auth/login.php')</code></td>
                                            <td><span class="badge bg-green">Fixed</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Testing Instructions -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Manual Testing Steps</h3>
                        </div>
                        <div class="card-body">
                            <div class="steps">
                                <div class="step-item">
                                    <div class="h4">1. Test Admin Login</div>
                                    <p>Navigate to <code>http://localhost/online_banking/auth/login.php</code></p>
                                    <ul>
                                        <li>Login with: <strong>admin / admin123</strong></li>
                                        <li>Should redirect to: <code>http://localhost/online_banking/admin/</code></li>
                                        <li>Should show admin dashboard with admin header</li>
                                    </ul>
                                </div>
                                
                                <div class="step-item">
                                    <div class="h4">2. Test User Login</div>
                                    <p>Logout and login as regular user</p>
                                    <ul>
                                        <li>Login with: <strong>john_doe / user123</strong></li>
                                        <li>Should redirect to: <code>http://localhost/online_banking/dashboard/</code></li>
                                        <li>Should show user dashboard with user header</li>
                                    </ul>
                                </div>
                                
                                <div class="step-item">
                                    <div class="h4">3. Test Main Index Redirect</div>
                                    <p>Navigate to <code>http://localhost/online_banking/</code></p>
                                    <ul>
                                        <li>If logged in as admin: Should redirect to admin dashboard</li>
                                        <li>If logged in as user: Should redirect to user dashboard</li>
                                        <li>If not logged in: Should redirect to login page</li>
                                    </ul>
                                </div>
                                
                                <div class="step-item">
                                    <div class="h4">4. Test Switch to User View</div>
                                    <p>From admin dashboard, click "Switch to User View"</p>
                                    <ul>
                                        <li>Should redirect to user dashboard</li>
                                        <li>Should show user interface (not admin interface)</li>
                                        <li>Admin should still be logged in but viewing user interface</li>
                                    </ul>
                                </div>
                                
                                <div class="step-item">
                                    <div class="h4">5. Test Direct Admin Page Access</div>
                                    <p>Try accessing admin pages directly without proper authentication</p>
                                    <ul>
                                        <li>Logout completely</li>
                                        <li>Try to access: <code>http://localhost/online_banking/admin/users.php</code></li>
                                        <li>Should redirect to login page</li>
                                        <li>Login as regular user and try again</li>
                                        <li>Should redirect to login page (not user dashboard)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Test Links -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Quick Test Links</h3>
                        </div>
                        <div class="card-body">
                            <div class="btn-list">
                                <a href="../auth/logout.php" class="btn btn-outline-secondary">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    Logout (Test Login Redirects)
                                </a>
                                <a href="../dashboard/" class="btn btn-outline-info">
                                    <i class="fas fa-eye me-2"></i>
                                    Switch to User View
                                </a>
                                <a href="../" class="btn btn-outline-primary">
                                    <i class="fas fa-home me-2"></i>
                                    Test Main Index Redirect
                                </a>
                                <a href="../auth/login.php" class="btn btn-outline-success">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Go to Login Page
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>

<?php
require_once 'config/config.php';

$message = '';
$error = '';

if (isset($_GET['setup'])) {
    try {
        $db = getDB();

        // Create OTP table
        $sql = "CREATE TABLE IF NOT EXISTS user_otps (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            otp_code VARCHAR(10) NOT NULL,
            expires_at DATETIME NOT NULL,
            used TINYINT(1) DEFAULT 0,
            used_at DATETIME NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_otp_code (otp_code),
            INDEX idx_expires_at (expires_at),
            FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE
        )";

        $db->query($sql);
        $message = "OTP table created successfully!";

    } catch (Exception $e) {
        $error = "Error creating OTP table: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Setup OTP Table</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 50px; }
        .message { color: green; padding: 10px; background: #f0f8ff; border: 1px solid green; }
        .error { color: red; padding: 10px; background: #ffe0e0; border: 1px solid red; }
        .button { background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Setup OTP Table</h1>

    <?php if ($message): ?>
        <div class="message"><?php echo $message; ?></div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="error"><?php echo $error; ?></div>
    <?php endif; ?>

    <?php if (!$message && !$error): ?>
        <p>Click the button below to create the OTP table for two-factor authentication:</p>
        <a href="?setup=1" class="button">Create OTP Table</a>
    <?php endif; ?>

    <?php if ($message): ?>
        <p><a href="auth/login.php">Go to Login Page</a></p>
    <?php endif; ?>
</body>
</html>

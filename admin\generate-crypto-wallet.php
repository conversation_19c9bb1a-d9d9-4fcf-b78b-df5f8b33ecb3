<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Generate Crypto Wallet';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        $account_id = intval($_POST['account_id']);
        $cryptocurrency = $_POST['cryptocurrency'];
        $wallet_name = trim($_POST['wallet_name']);
        $wallet_type = $_POST['wallet_type'];
        $daily_limit = floatval($_POST['daily_limit']);
        $monthly_limit = floatval($_POST['monthly_limit']);
        $initial_balance = floatval($_POST['initial_balance']);
        
        // Validate inputs
        if (empty($account_id) || empty($cryptocurrency) || empty($wallet_name)) {
            throw new Exception("Account, cryptocurrency, and wallet name are required.");
        }
        
        if ($daily_limit > $monthly_limit) {
            throw new Exception("Daily limit cannot exceed monthly limit.");
        }
        
        // Check if user already has a wallet for this cryptocurrency
        $existing_check = "SELECT wallet_id FROM crypto_wallets WHERE account_id = ? AND cryptocurrency = ?";
        $existing_result = $db->query($existing_check, [$account_id, $cryptocurrency]);
        if ($existing_result->num_rows > 0) {
            throw new Exception("User already has a {$cryptocurrency} wallet. Each user can only have one wallet per cryptocurrency.");
        }
        
        // Generate wallet address based on cryptocurrency
        function generateWalletAddress($crypto) {
            $prefixes = [
                'BTC' => ['1', '3', 'bc1'],
                'ETH' => ['0x'],
                'LTC' => ['L', 'M', 'ltc1'],
                'BCH' => ['1', '3', 'q'],
                'ADA' => ['addr1'],
                'DOT' => ['1']
            ];
            
            $prefix = $prefixes[$crypto][array_rand($prefixes[$crypto])];
            $length = ($crypto === 'ETH') ? 40 : (($crypto === 'ADA') ? 58 : 34);
            
            $chars = '**********************************************************';
            $address = $prefix;
            
            for ($i = strlen($prefix); $i < $length; $i++) {
                $address .= $chars[rand(0, strlen($chars) - 1)];
            }
            
            return $address;
        }
        
        // Generate wallet data
        $wallet_address = generateWalletAddress($cryptocurrency);
        $private_key = bin2hex(random_bytes(32));
        $public_key = bin2hex(random_bytes(33));
        
        // Insert crypto wallet
        $insert_wallet = "INSERT INTO crypto_wallets (
            account_id, wallet_address, wallet_name, cryptocurrency, wallet_balance,
            private_key, public_key, wallet_type, daily_limit, monthly_limit, 
            status, approved_by, approved_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW())";
        
        $wallet_id = $db->insert($insert_wallet, [
            $account_id, $wallet_address, $wallet_name, $cryptocurrency, $initial_balance,
            $private_key, $public_key, $wallet_type, $daily_limit, $monthly_limit, $_SESSION['user_id']
        ]);
        
        if ($wallet_id) {
            // Create initial balance transaction if balance > 0
            if ($initial_balance > 0) {
                $transaction_hash = bin2hex(random_bytes(32));
                
                $insert_transaction = "INSERT INTO crypto_transactions (
                    wallet_id, account_id, transaction_type, amount, cryptocurrency, description, 
                    transaction_hash, status, processed_by
                ) VALUES (?, ?, 'credit', ?, ?, ?, ?, 'confirmed', ?)";
                
                $db->query($insert_transaction, [
                    $wallet_id, $account_id, $initial_balance, $cryptocurrency,
                    "Initial wallet balance - Wallet generation", $transaction_hash, $_SESSION['user_id']
                ]);
            }
            
            $success = "✅ Crypto wallet generated successfully! Wallet ID: {$wallet_id} | Address: {$wallet_address}";
        } else {
            throw new Exception("Failed to generate crypto wallet.");
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get all users for the dropdown
try {
    $db = getDB();
    $users_query = "SELECT id, first_name, last_name, username, account_number FROM accounts WHERE is_admin = 0 ORDER BY first_name, last_name";
    $users_result = $db->query($users_query);
    $users = [];
    while ($row = $users_result->fetch_assoc()) {
        $users[] = $row;
    }
} catch (Exception $e) {
    $users = [];
    $error = "Failed to load users: " . $e->getMessage();
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="crypto-wallets.php">Crypto Wallets</a></li>
        <li class="breadcrumb-item active" aria-current="page">Generate Wallet</li>
    </ol>
</nav>

<!-- Success Message -->
<?php if (isset($success)): ?>
<div class="alert alert-success alert-dismissible" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo $success; ?></div>
        </div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger alert-dismissible" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<div class="row">
    <!-- Generation Form -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-plus me-2"></i>
                    Generate Cryptocurrency Wallet
                </h3>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Select User <span class="text-danger">*</span></label>
                                <select name="account_id" class="form-select" required>
                                    <option value="">Choose a user...</option>
                                    <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>" <?php echo ($_POST['account_id'] ?? '') == $user['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?> 
                                        (@<?php echo htmlspecialchars($user['username']); ?>) - 
                                        <?php echo htmlspecialchars($user['account_number']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Cryptocurrency <span class="text-danger">*</span></label>
                                <select name="cryptocurrency" class="form-select" required onchange="updateCryptoInfo(this)">
                                    <option value="">Select cryptocurrency...</option>
                                    <option value="BTC" <?php echo ($_POST['cryptocurrency'] ?? '') === 'BTC' ? 'selected' : ''; ?>>Bitcoin (BTC)</option>
                                    <option value="ETH" <?php echo ($_POST['cryptocurrency'] ?? '') === 'ETH' ? 'selected' : ''; ?>>Ethereum (ETH)</option>
                                    <option value="LTC" <?php echo ($_POST['cryptocurrency'] ?? '') === 'LTC' ? 'selected' : ''; ?>>Litecoin (LTC)</option>
                                    <option value="BCH" <?php echo ($_POST['cryptocurrency'] ?? '') === 'BCH' ? 'selected' : ''; ?>>Bitcoin Cash (BCH)</option>
                                    <option value="ADA" <?php echo ($_POST['cryptocurrency'] ?? '') === 'ADA' ? 'selected' : ''; ?>>Cardano (ADA)</option>
                                    <option value="DOT" <?php echo ($_POST['cryptocurrency'] ?? '') === 'DOT' ? 'selected' : ''; ?>>Polkadot (DOT)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Wallet Name <span class="text-danger">*</span></label>
                                <input type="text" name="wallet_name" class="form-control" 
                                       placeholder="e.g., John's Bitcoin Wallet" 
                                       value="<?php echo $_POST['wallet_name'] ?? ''; ?>" required>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Wallet Type</label>
                                <select name="wallet_type" class="form-select">
                                    <option value="standard" <?php echo ($_POST['wallet_type'] ?? 'standard') === 'standard' ? 'selected' : ''; ?>>Standard</option>
                                    <option value="premium" <?php echo ($_POST['wallet_type'] ?? '') === 'premium' ? 'selected' : ''; ?>>Premium</option>
                                    <option value="enterprise" <?php echo ($_POST['wallet_type'] ?? '') === 'enterprise' ? 'selected' : ''; ?>>Enterprise</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Daily Transaction Limit <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" name="daily_limit" class="form-control" 
                                           step="0.00000001" min="0.00000001" max="1000000000" 
                                           value="<?php echo $_POST['daily_limit'] ?? '100'; ?>" required>
                                    <span class="input-group-text" id="daily-crypto">CRYPTO</span>
                                </div>
                                <small class="form-hint">Maximum daily transaction amount</small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Monthly Transaction Limit <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" name="monthly_limit" class="form-control" 
                                           step="0.00000001" min="0.00000001" max="1000000000" 
                                           value="<?php echo $_POST['monthly_limit'] ?? '3000'; ?>" required>
                                    <span class="input-group-text" id="monthly-crypto">CRYPTO</span>
                                </div>
                                <small class="form-hint">Maximum monthly transaction amount</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Initial Wallet Balance</label>
                                <div class="input-group">
                                    <input type="number" name="initial_balance" class="form-control" 
                                           step="0.00000001" min="0" max="1000000000" 
                                           value="<?php echo $_POST['initial_balance'] ?? '0'; ?>">
                                    <span class="input-group-text" id="balance-crypto">CRYPTO</span>
                                </div>
                                <small class="form-hint">Optional: Add initial balance to the wallet</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-footer">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Generate Crypto Wallet
                        </button>
                        <a href="crypto-wallets.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Wallets
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Guidelines -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    Wallet Generation Guidelines
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h4 class="alert-title">Wallet Creation</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li>Each user can have one wallet per cryptocurrency</li>
                        <li>Wallets are activated immediately</li>
                        <li>Unique addresses generated automatically</li>
                        <li>Private/public keys created securely</li>
                    </ul>
                </div>
                
                <div class="alert alert-warning">
                    <h4 class="alert-title">Transaction Limits</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li>Daily limit: 0.00000001 - 1,000,000,000</li>
                        <li>Monthly limit: 0.00000001 - 1,000,000,000</li>
                        <li>Daily limit cannot exceed monthly limit</li>
                        <li>Limits can be modified later</li>
                        <li>High-value wallets for enterprise clients</li>
                    </ul>
                </div>
                
                <div class="alert alert-success">
                    <h4 class="alert-title">Security Features</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li>Secure private key generation</li>
                        <li>Unique wallet addresses</li>
                        <li>Blockchain-style transaction hashing</li>
                        <li>Admin approval tracking</li>
                        <li>Complete audit trail</li>
                    </ul>
                </div>
                
                <div class="alert alert-primary">
                    <h4 class="alert-title">Supported Cryptocurrencies</h4>
                    <ul class="mb-0" style="font-size: 0.9rem;">
                        <li><i class="fab fa-bitcoin text-warning"></i> Bitcoin (BTC)</li>
                        <li><i class="fab fa-ethereum text-info"></i> Ethereum (ETH)</li>
                        <li><i class="fas fa-coins text-secondary"></i> Litecoin (LTC)</li>
                        <li><i class="fab fa-bitcoin text-success"></i> Bitcoin Cash (BCH)</li>
                        <li><i class="fas fa-coins text-primary"></i> Cardano (ADA)</li>
                        <li><i class="fas fa-circle text-danger"></i> Polkadot (DOT)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateCryptoInfo(select) {
    const crypto = select.value;
    const dailyCrypto = document.getElementById('daily-crypto');
    const monthlyCrypto = document.getElementById('monthly-crypto');
    const balanceCrypto = document.getElementById('balance-crypto');
    
    if (crypto) {
        dailyCrypto.textContent = crypto;
        monthlyCrypto.textContent = crypto;
        balanceCrypto.textContent = crypto;
    } else {
        dailyCrypto.textContent = 'CRYPTO';
        monthlyCrypto.textContent = 'CRYPTO';
        balanceCrypto.textContent = 'CRYPTO';
    }
}
</script>

<?php include 'includes/admin-footer.php'; ?>

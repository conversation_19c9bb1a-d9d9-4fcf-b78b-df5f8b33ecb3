<?php
require_once 'config/config.php';

$message = '';
$error = '';

if (isset($_GET['update'])) {
    try {
        $db = getDB();
        
        // Check if source column already exists
        $check_sql = "SHOW COLUMNS FROM user_otps LIKE 'source'";
        $check_result = $db->query($check_sql);
        
        if ($check_result && $check_result->num_rows > 0) {
            $message = "Source column already exists in user_otps table!";
        } else {
            // Add source column to track OTP origin
            $sql = "ALTER TABLE user_otps ADD COLUMN source VARCHAR(20) DEFAULT 'login' AFTER expires_at";
            $db->query($sql);
            
            // Update existing records to have 'login' as source
            $update_sql = "UPDATE user_otps SET source = 'login' WHERE source IS NULL";
            $db->query($update_sql);
            
            $message = "Successfully added 'source' column to user_otps table!<br>
                       Now both email OTPs and admin-generated OTPs can work simultaneously.";
        }
        
    } catch (Exception $e) {
        $error = "Error updating OTP table: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Update OTP Table</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 50px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 20px; margin: -30px -30px 30px -30px; border-radius: 10px 10px 0 0; }
        .message { color: green; padding: 15px; background: #f0f8ff; border: 1px solid green; border-radius: 5px; margin: 15px 0; }
        .error { color: red; padding: 15px; background: #ffe0e0; border: 1px solid red; border-radius: 5px; margin: 15px 0; }
        .button { background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 10px 10px 0; }
        .button:hover { background: #218838; color: white; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #007cba; }
        .feature { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; border-left: 3px solid #28a745; }
        h1 { color: #333; margin: 0; }
        ul { padding-left: 20px; }
        li { margin: 8px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Update OTP Table</h1>
            <p>Enable multiple simultaneous OTP codes</p>
        </div>
        
        <div class="info">
            <strong>What this update does:</strong><br>
            Adds a 'source' column to track whether an OTP was generated during login or by an admin. 
            This allows both email OTPs and admin-generated OTPs to work at the same time.
        </div>
        
        <?php if ($message): ?>
            <div class="message"><?php echo $message; ?></div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="error"><?php echo $error; ?></div>
        <?php endif; ?>
        
        <?php if (!$message && !$error): ?>
            <div class="feature">
                <h3>🎯 New Features After Update:</h3>
                <ul>
                    <li><strong>Email OTP:</strong> Generated when user logs in</li>
                    <li><strong>Admin OTP:</strong> Generated by admin for support</li>
                    <li><strong>Both Work:</strong> User can use either code to log in</li>
                    <li><strong>Smart Management:</strong> Login OTPs replace old login OTPs, admin OTPs are additional</li>
                </ul>
            </div>
            
            <div class="feature">
                <h3>📋 How It Works:</h3>
                <ul>
                    <li>User tries to log in → Email OTP sent</li>
                    <li>User can't receive email → Admin generates backup OTP</li>
                    <li>User can use EITHER the email OTP OR the admin OTP</li>
                    <li>Both codes are valid until expiration (10 minutes)</li>
                </ul>
            </div>
            
            <a href="?update=1" class="button">🚀 Update OTP Table</a>
        <?php endif; ?>
        
        <?php if ($message): ?>
            <h3>✅ Next Steps:</h3>
            <p>1. <a href="auth/login.php">Test the login process</a></p>
            <p>2. <a href="admin/users.php">Generate admin OTP</a></p>
            <p>3. <a href="create_test_user.php">Create test users</a> (if needed)</p>
            
            <div class="feature">
                <h3>🧪 Testing Scenario:</h3>
                <ol>
                    <li>User tries to log in (email OTP sent)</li>
                    <li>Admin generates additional OTP</li>
                    <li>User can use either code to complete login</li>
                    <li>Both codes show in admin panel</li>
                </ol>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>

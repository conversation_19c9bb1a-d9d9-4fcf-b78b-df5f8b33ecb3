<?php
/**
 * Enhanced Error Logger for Online Banking System
 * Provides comprehensive error logging with database storage and file logging
 */

class ErrorLogger
{
    private $db;
    private $log_file;
    private $max_log_size = ********; // 10MB
    
    public function __construct($database = null)
    {
        $this->db = $database ?: getDB();
        $this->log_file = __DIR__ . '/../logs/error.log';
        $this->ensureLogDirectory();
        $this->createErrorLogTable();
    }
    
    /**
     * Ensure log directory exists
     */
    private function ensureLogDirectory()
    {
        $log_dir = dirname($this->log_file);
        if (!is_dir($log_dir)) {
            mkdir($log_dir, 0755, true);
        }
    }
    
    /**
     * Create error log table if it doesn't exist
     */
    private function createErrorLogTable()
    {
        try {
            $create_table = "
            CREATE TABLE IF NOT EXISTS error_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                error_level VARCHAR(20) NOT NULL,
                error_message TEXT NOT NULL,
                error_file VARCHAR(500) NULL,
                error_line INT NULL,
                error_context TEXT NULL,
                user_id INT NULL,
                user_ip VARCHAR(45) NULL,
                user_agent TEXT NULL,
                request_uri VARCHAR(1000) NULL,
                request_method VARCHAR(10) NULL,
                request_data TEXT NULL,
                stack_trace TEXT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_error_level (error_level),
                INDEX idx_created_at (created_at),
                INDEX idx_user_id (user_id),
                FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE SET NULL
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            ";
            
            $this->db->query($create_table);
        } catch (Exception $e) {
            // Fallback to file logging only if database fails
            error_log("ErrorLogger: Failed to create error_logs table - " . $e->getMessage());
        }
    }
    
    /**
     * Log error with comprehensive details
     */
    public function logError($level, $message, $file = null, $line = null, $context = null)
    {
        $error_data = [
            'level' => strtoupper($level),
            'message' => $message,
            'file' => $file,
            'line' => $line,
            'context' => is_array($context) ? json_encode($context) : $context,
            'user_id' => $_SESSION['user_id'] ?? null,
            'user_ip' => $this->getUserIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null,
            'request_uri' => $_SERVER['REQUEST_URI'] ?? null,
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? null,
            'request_data' => $this->getRequestData(),
            'stack_trace' => $this->getStackTrace(),
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // Log to database
        $this->logToDatabase($error_data);
        
        // Log to file
        $this->logToFile($error_data);
        
        // Log to PHP error log
        $this->logToPHPErrorLog($error_data);
        
        return $error_data;
    }
    
    /**
     * Log to database
     */
    private function logToDatabase($error_data)
    {
        try {
            $insert_error = "INSERT INTO error_logs (
                error_level, error_message, error_file, error_line, error_context,
                user_id, user_ip, user_agent, request_uri, request_method,
                request_data, stack_trace
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $this->db->query($insert_error, [
                $error_data['level'],
                $error_data['message'],
                $error_data['file'],
                $error_data['line'],
                $error_data['context'],
                $error_data['user_id'],
                $error_data['user_ip'],
                $error_data['user_agent'],
                $error_data['request_uri'],
                $error_data['request_method'],
                $error_data['request_data'],
                $error_data['stack_trace']
            ]);
        } catch (Exception $e) {
            // Fallback to file logging if database fails
            error_log("ErrorLogger: Database logging failed - " . $e->getMessage());
        }
    }
    
    /**
     * Log to file
     */
    private function logToFile($error_data)
    {
        try {
            // Rotate log file if too large
            if (file_exists($this->log_file) && filesize($this->log_file) > $this->max_log_size) {
                $this->rotateLogFile();
            }
            
            $log_entry = sprintf(
                "[%s] %s: %s in %s:%d\n" .
                "User: %s (IP: %s)\n" .
                "Request: %s %s\n" .
                "Context: %s\n" .
                "Stack Trace: %s\n" .
                "---\n",
                $error_data['timestamp'],
                $error_data['level'],
                $error_data['message'],
                $error_data['file'] ?? 'unknown',
                $error_data['line'] ?? 0,
                $error_data['user_id'] ?? 'guest',
                $error_data['user_ip'],
                $error_data['request_method'],
                $error_data['request_uri'],
                $error_data['context'] ?? 'none',
                $error_data['stack_trace'] ?? 'none'
            );
            
            file_put_contents($this->log_file, $log_entry, FILE_APPEND | LOCK_EX);
        } catch (Exception $e) {
            error_log("ErrorLogger: File logging failed - " . $e->getMessage());
        }
    }
    
    /**
     * Log to PHP error log
     */
    private function logToPHPErrorLog($error_data)
    {
        $php_log_entry = sprintf(
            "Banking System [%s]: %s in %s:%d (User: %s, IP: %s)",
            $error_data['level'],
            $error_data['message'],
            $error_data['file'] ?? 'unknown',
            $error_data['line'] ?? 0,
            $error_data['user_id'] ?? 'guest',
            $error_data['user_ip']
        );
        
        error_log($php_log_entry);
    }
    
    /**
     * Get user IP address
     */
    private function getUserIP()
    {
        $ip_keys = ['HTTP_CF_CONNECTING_IP', 'HTTP_X_FORWARDED_FOR', 'HTTP_X_FORWARDED', 'HTTP_FORWARDED_FOR', 'HTTP_FORWARDED', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                return trim($ips[0]);
            }
        }
        
        return 'unknown';
    }
    
    /**
     * Get request data
     */
    private function getRequestData()
    {
        $data = [];
        
        if (!empty($_POST)) {
            $post_data = $_POST;
            // Remove sensitive data
            $sensitive_fields = ['password', 'smtp_password', 'confirm_password', 'current_password', 'new_password'];
            foreach ($sensitive_fields as $field) {
                if (isset($post_data[$field])) {
                    $post_data[$field] = '[REDACTED]';
                }
            }
            $data['POST'] = $post_data;
        }
        
        if (!empty($_GET)) {
            $data['GET'] = $_GET;
        }
        
        return !empty($data) ? json_encode($data) : null;
    }
    
    /**
     * Get stack trace
     */
    private function getStackTrace()
    {
        $trace = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);
        $formatted_trace = [];
        
        foreach ($trace as $i => $frame) {
            if ($i === 0) continue; // Skip this function
            
            $formatted_trace[] = sprintf(
                "#%d %s(%d): %s%s%s()",
                $i - 1,
                $frame['file'] ?? 'unknown',
                $frame['line'] ?? 0,
                $frame['class'] ?? '',
                $frame['type'] ?? '',
                $frame['function'] ?? 'unknown'
            );
        }
        
        return implode("\n", array_slice($formatted_trace, 0, 10)); // Limit to 10 frames
    }
    
    /**
     * Rotate log file
     */
    private function rotateLogFile()
    {
        $backup_file = $this->log_file . '.' . date('Y-m-d-H-i-s') . '.bak';
        rename($this->log_file, $backup_file);
        
        // Keep only last 5 backup files
        $log_dir = dirname($this->log_file);
        $backup_files = glob($log_dir . '/error.log.*.bak');
        if (count($backup_files) > 5) {
            usort($backup_files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            $files_to_delete = array_slice($backup_files, 0, count($backup_files) - 5);
            foreach ($files_to_delete as $file) {
                unlink($file);
            }
        }
    }
    
    /**
     * Get recent errors from database
     */
    public function getRecentErrors($limit = 50, $level = null)
    {
        try {
            $where_clause = $level ? "WHERE error_level = ?" : "";
            $params = $level ? [$level] : [];
            
            $query = "SELECT el.*, a.first_name, a.last_name, a.username 
                     FROM error_logs el 
                     LEFT JOIN accounts a ON el.user_id = a.id 
                     $where_clause
                     ORDER BY el.created_at DESC 
                     LIMIT ?";
            
            $params[] = $limit;
            $result = $this->db->query($query, $params);
            
            $errors = [];
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $errors[] = $row;
                }
            }
            
            return $errors;
        } catch (Exception $e) {
            error_log("ErrorLogger: Failed to get recent errors - " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get error statistics
     */
    public function getErrorStats($hours = 24)
    {
        try {
            $query = "SELECT 
                     error_level,
                     COUNT(*) as count,
                     MAX(created_at) as last_occurrence
                     FROM error_logs 
                     WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? HOUR)
                     GROUP BY error_level
                     ORDER BY count DESC";
            
            $result = $this->db->query($query, [$hours]);
            
            $stats = [];
            if ($result) {
                while ($row = $result->fetch_assoc()) {
                    $stats[] = $row;
                }
            }
            
            return $stats;
        } catch (Exception $e) {
            error_log("ErrorLogger: Failed to get error stats - " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Clear old errors
     */
    public function clearOldErrors($days = 30)
    {
        try {
            $query = "DELETE FROM error_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
            $result = $this->db->query($query, [$days]);
            return $this->db->affected_rows();
        } catch (Exception $e) {
            error_log("ErrorLogger: Failed to clear old errors - " . $e->getMessage());
            return 0;
        }
    }
}

/**
 * Global error handler function
 */
function bankingErrorHandler($errno, $errstr, $errfile, $errline)
{
    // Don't log errors that are suppressed with @
    if (!(error_reporting() & $errno)) {
        return false;
    }
    
    $error_levels = [
        E_ERROR => 'ERROR',
        E_WARNING => 'WARNING',
        E_PARSE => 'PARSE',
        E_NOTICE => 'NOTICE',
        E_CORE_ERROR => 'CORE_ERROR',
        E_CORE_WARNING => 'CORE_WARNING',
        E_COMPILE_ERROR => 'COMPILE_ERROR',
        E_COMPILE_WARNING => 'COMPILE_WARNING',
        E_USER_ERROR => 'USER_ERROR',
        E_USER_WARNING => 'USER_WARNING',
        E_USER_NOTICE => 'USER_NOTICE',
        E_STRICT => 'STRICT',
        E_RECOVERABLE_ERROR => 'RECOVERABLE_ERROR',
        E_DEPRECATED => 'DEPRECATED',
        E_USER_DEPRECATED => 'USER_DEPRECATED'
    ];
    
    $level = $error_levels[$errno] ?? 'UNKNOWN';
    
    try {
        $logger = new ErrorLogger();
        $logger->logError($level, $errstr, $errfile, $errline);
    } catch (Exception $e) {
        // Fallback to basic error logging
        error_log("Banking System Error Handler Failed: " . $e->getMessage());
        error_log("Original Error: [$level] $errstr in $errfile:$errline");
    }
    
    // Don't execute PHP internal error handler
    return true;
}

/**
 * Global exception handler function
 */
function bankingExceptionHandler($exception)
{
    try {
        $logger = new ErrorLogger();
        $logger->logError(
            'EXCEPTION',
            $exception->getMessage(),
            $exception->getFile(),
            $exception->getLine(),
            [
                'exception_class' => get_class($exception),
                'trace' => $exception->getTraceAsString()
            ]
        );
    } catch (Exception $e) {
        // Fallback to basic error logging
        error_log("Banking System Exception Handler Failed: " . $e->getMessage());
        error_log("Original Exception: " . $exception->getMessage() . " in " . $exception->getFile() . ":" . $exception->getLine());
    }
}

// Set global error and exception handlers
set_error_handler('bankingErrorHandler');
set_exception_handler('bankingExceptionHandler');
?>

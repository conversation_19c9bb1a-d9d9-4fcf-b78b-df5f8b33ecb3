/* Modern Banking Login Page - Nucleus Inspired */

:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --secondary-color: #f8fafc;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    --border-color: #e5e7eb;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    width: 100%;
    overflow-x: hidden;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #f8fafc;
}

.login-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    width: 100vw;
    height: 100vh;
    background: white;
    overflow: hidden;
}

/* Left Side - Hero Section */
.login-hero {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.login-hero::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 3rem;
}

.hero-logo-icon {
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    font-weight: 700;
    font-size: 1.25rem;
}

.hero-logo-text {
    color: white;
    font-size: 1.5rem;
    font-weight: 700;
}

.hero-title {
    color: white;
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.125rem;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.hero-testimonial {
    border-left: 4px solid var(--primary-color);
    padding-left: 1.5rem;
    margin-top: 2rem;
}

.testimonial-quote {
    color: white;
    font-size: 1.125rem;
    font-style: italic;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.testimonial-author {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.testimonial-role {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.875rem;
}

/* Right Side - Login Form */
.login-form-section {
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-title {
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.form-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    line-height: 1.5;
}

.login-form {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-input {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
    color: var(--text-primary);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-input::placeholder {
    color: var(--text-muted);
}

.password-input-group {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: var(--text-secondary);
}

.forgot-password {
    text-align: right;
    margin-top: 0.5rem;
}

.forgot-password a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.3s ease;
}

.forgot-password a:hover {
    color: var(--primary-dark);
}

.remember-me {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.remember-checkbox {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    background: white;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
}

.remember-checkbox:checked {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.remember-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.remember-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    cursor: pointer;
}

.login-button {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 1rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.login-button:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 10px 20px rgba(99, 102, 241, 0.3);
}

.login-button:active {
    transform: translateY(0);
}

.divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
    color: var(--text-muted);
    font-size: 0.875rem;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
    z-index: 1;
}

.divider span {
    background: white;
    padding: 0 1rem;
    position: relative;
    z-index: 2;
}

.google-login {
    width: 100%;
    background: white;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 2rem;
}

.google-login:hover {
    border-color: var(--text-muted);
    background: #f9fafb;
}

.signup-link {
    text-align: center;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.signup-link a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.signup-link a:hover {
    color: var(--primary-dark);
}

.alert {
    padding: 1rem;
    border-radius: 12px;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.alert-error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.alert-warning {
    background: #fffbeb;
    color: #d97706;
    border: 1px solid #fed7aa;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .login-container {
        grid-template-columns: 1fr;
        width: 100vw;
        height: 100vh;
    }

    .login-hero {
        display: none;
    }

    .login-form-section {
        padding: 2rem;
        width: 100%;
        height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .login-form-section {
        padding: 1.5rem;
    }

    .form-title {
        font-size: 1.75rem;
    }

    .form-subtitle {
        font-size: 0.875rem;
    }
}

@media (max-width: 480px) {
    .login-form-section {
        padding: 1rem;
    }

    .form-title {
        font-size: 1.5rem;
    }

    .form-header {
        margin-bottom: 1.5rem;
    }

    .form-group {
        margin-bottom: 1.25rem;
    }
}

/* Landscape mobile devices */
@media (max-height: 600px) and (orientation: landscape) {
    .login-form-section {
        padding: 1rem;
        justify-content: flex-start;
        overflow-y: auto;
    }

    .form-header {
        margin-bottom: 1rem;
    }

    .form-title {
        font-size: 1.5rem;
    }

    .form-subtitle {
        font-size: 0.875rem;
    }
}

/* Loading Animation */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

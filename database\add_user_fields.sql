-- Add missing fields to accounts table
-- Run this script to update existing database

USE online_banking;

-- Add occupation field
ALTER TABLE accounts ADD COLUMN occupation VARCHAR(100) AFTER date_of_birth;

-- Add marital status field
ALTER TABLE accounts ADD COLUMN marital_status ENUM('single', 'married', 'divorced', 'widowed') DEFAULT 'single' AFTER occupation;

-- Add gender field
ALTER TABLE accounts ADD COLUMN gender ENUM('male', 'female', 'other') DEFAULT 'male' AFTER marital_status;

-- Add currency field
ALTER TABLE accounts ADD COLUMN currency VARCHAR(3) DEFAULT 'USD' AFTER gender;

-- Update existing records to have default values
UPDATE accounts SET 
    occupation = 'Not Specified',
    marital_status = 'single',
    gender = 'male',
    currency = 'USD'
WHERE occupation IS NULL OR marital_status IS NULL OR gender IS NULL OR currency IS NULL;

<?php
require_once '../config/config.php';
requireAdmin();

$page_title = '2FA Configuration';

// Include Google2FA class
require_once '../vendor/GoogleAuthenticator/Google2FA.php';
$google2fa = new Google2FA();

// Initialize database connection
$db = getDB();

$success = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'enable_global_2fa':
                    // Enable Google 2FA globally
                    $update_sql = "UPDATE security_settings SET google_2fa_enabled = 1, updated_by = ? WHERE is_active = 1";
                    $db->query($update_sql, [$_SESSION['user_id']]);
                    $success = 'Google 2FA has been enabled globally.';
                    break;
                    
                case 'disable_global_2fa':
                    // Disable Google 2FA globally
                    $update_sql = "UPDATE security_settings SET google_2fa_enabled = 0, updated_by = ? WHERE is_active = 1";
                    $db->query($update_sql, [$_SESSION['user_id']]);
                    $success = 'Google 2FA has been disabled globally.';
                    break;
                    
                case 'reset_user_2fa':
                    $user_id = intval($_POST['user_id']);
                    if ($user_id > 0) {
                        // Reset user's Google 2FA
                        $reset_sql = "UPDATE user_security_settings SET google_2fa_enabled = 0, google_2fa_secret = NULL WHERE user_id = ?";
                        $db->query($reset_sql, [$user_id]);
                        $success = 'User Google 2FA has been reset successfully.';
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        error_log("2FA Configuration Error: " . $e->getMessage());
        $error = 'An error occurred while updating 2FA settings.';
    }
}

// Get current global settings
$global_settings_query = "SELECT * FROM security_settings WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1";
$global_result = $db->query($global_settings_query);
$global_settings = $global_result ? $global_result->fetch_assoc() : null;

// Get users with 2FA enabled
$users_2fa_query = "SELECT a.id, a.username, a.first_name, a.last_name, a.email, 
                           uss.google_2fa_enabled, uss.google_2fa_secret, uss.created_at as setup_date
                    FROM accounts a 
                    LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
                    WHERE a.is_admin = 0 AND uss.google_2fa_enabled = 1
                    ORDER BY a.first_name, a.last_name";
$users_result = $db->query($users_2fa_query);
$users_with_2fa = [];
if ($users_result) {
    while ($row = $users_result->fetch_assoc()) {
        $users_with_2fa[] = $row;
    }
}

// Get all users for reset dropdown
$all_users_query = "SELECT id, username, first_name, last_name, email FROM accounts WHERE is_admin = 0 ORDER BY first_name, last_name";
$all_users_result = $db->query($all_users_query);
$all_users = [];
if ($all_users_result) {
    while ($row = $all_users_result->fetch_assoc()) {
        $all_users[] = $row;
    }
}

include '../admin/includes/header.php';
?>

<div class="page-wrapper">
    <?php include '../admin/includes/sidebar.php'; ?>
    
    <div class="page-content">
        <div class="container-xl">
            <!-- Page header -->
            <div class="page-header d-print-none">
                <div class="row align-items-center">
                    <div class="col">
                        <h2 class="page-title">2FA Configuration</h2>
                        <div class="text-muted mt-1">Manage Google Authenticator 2FA settings</div>
                    </div>
                    <div class="col-auto ms-auto d-print-none">
                        <div class="btn-list">
                            <a href="google-2fa-setup.php" class="btn btn-primary">
                                <i class="ti ti-qrcode me-1"></i>
                                Setup User 2FA
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible" role="alert">
                    <div class="d-flex">
                        <div><i class="ti ti-check alert-icon"></i></div>
                        <div><?php echo htmlspecialchars($success); ?></div>
                    </div>
                    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible" role="alert">
                    <div class="d-flex">
                        <div><i class="ti ti-alert-circle alert-icon"></i></div>
                        <div><?php echo htmlspecialchars($error); ?></div>
                    </div>
                    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                </div>
            <?php endif; ?>

            <div class="row">
                <!-- Global 2FA Settings -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Global 2FA Settings</h3>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label class="form-label">Current Status</label>
                                <div class="form-control-plaintext">
                                    <?php if ($global_settings && $global_settings['google_2fa_enabled']): ?>
                                        <span class="badge bg-success">Enabled</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Disabled</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <div class="btn-list">
                                <?php if (!$global_settings || !$global_settings['google_2fa_enabled']): ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="enable_global_2fa">
                                        <button type="submit" class="btn btn-success">
                                            <i class="ti ti-shield-check me-1"></i>
                                            Enable Global 2FA
                                        </button>
                                    </form>
                                <?php else: ?>
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="action" value="disable_global_2fa">
                                        <button type="submit" class="btn btn-warning" onclick="return confirm('Are you sure you want to disable global 2FA?')">
                                            <i class="ti ti-shield-off me-1"></i>
                                            Disable Global 2FA
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reset User 2FA -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Reset User 2FA</h3>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <input type="hidden" name="action" value="reset_user_2fa">
                                <div class="mb-3">
                                    <label class="form-label">Select User</label>
                                    <select name="user_id" class="form-select" required>
                                        <option value="">Choose a user...</option>
                                        <?php foreach ($all_users as $user): ?>
                                            <option value="<?php echo $user['id']; ?>">
                                                <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name'] . ' (' . $user['username'] . ')'); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to reset this user\'s 2FA? They will need to set it up again.')">
                                    <i class="ti ti-refresh me-1"></i>
                                    Reset 2FA
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Users with 2FA Enabled -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Users with 2FA Enabled (<?php echo count($users_with_2fa); ?>)</h3>
                        </div>
                        <div class="card-body">
                            <?php if (empty($users_with_2fa)): ?>
                                <div class="empty">
                                    <div class="empty-icon">
                                        <i class="ti ti-shield-off"></i>
                                    </div>
                                    <p class="empty-title">No users have 2FA enabled</p>
                                    <p class="empty-subtitle text-muted">Users can set up 2FA from their security settings or you can help them set it up.</p>
                                    <div class="empty-action">
                                        <a href="google-2fa-setup.php" class="btn btn-primary">
                                            <i class="ti ti-qrcode me-1"></i>
                                            Setup User 2FA
                                        </a>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-vcenter">
                                        <thead>
                                            <tr>
                                                <th>User</th>
                                                <th>Email</th>
                                                <th>Setup Date</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($users_with_2fa as $user): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex py-1 align-items-center">
                                                            <span class="avatar me-2"><?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?></span>
                                                            <div class="flex-fill">
                                                                <div class="font-weight-medium"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                                                                <div class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                                    <td><?php echo date('M d, Y', strtotime($user['setup_date'])); ?></td>
                                                    <td><span class="badge bg-success">Active</span></td>
                                                    <td>
                                                        <form method="POST" style="display: inline;">
                                                            <input type="hidden" name="action" value="reset_user_2fa">
                                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Reset 2FA for this user?')">
                                                                <i class="ti ti-refresh"></i>
                                                                Reset
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../admin/includes/footer.php'; ?>

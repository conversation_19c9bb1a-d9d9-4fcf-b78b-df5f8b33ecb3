<?php
require_once '../config/config.php';
requireAdmin();

$page_title = '2FA Configuration';

// Include Google2FA class
require_once '../vendor/GoogleAuthenticator/Google2FA.php';
$google2fa = new Google2FA();

// Initialize database connection
$db = getDB();

$success = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'enable_global_2fa':
                    // Enable Google 2FA globally
                    $update_sql = "UPDATE security_settings SET google_2fa_enabled = 1, updated_by = ? WHERE is_active = 1";
                    $db->query($update_sql, [$_SESSION['user_id']]);
                    $success = 'Google 2FA has been enabled globally.';
                    break;
                    
                case 'disable_global_2fa':
                    // Disable Google 2FA globally
                    $update_sql = "UPDATE security_settings SET google_2fa_enabled = 0, updated_by = ? WHERE is_active = 1";
                    $db->query($update_sql, [$_SESSION['user_id']]);
                    $success = 'Google 2FA has been disabled globally.';
                    break;
                    
                case 'reset_user_2fa':
                    $user_id = intval($_POST['user_id']);
                    if ($user_id > 0) {
                        // Reset user's Google 2FA
                        $reset_sql = "UPDATE user_security_settings SET google_2fa_enabled = 0, google_2fa_secret = NULL WHERE user_id = ?";
                        $db->query($reset_sql, [$user_id]);
                        $success = 'User Google 2FA has been reset successfully.';
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        error_log("2FA Configuration Error: " . $e->getMessage());
        $error = 'An error occurred while updating 2FA settings.';
    }
}

// Get current global settings
$global_settings_query = "SELECT * FROM security_settings WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1";
$global_result = $db->query($global_settings_query);
$global_settings = $global_result ? $global_result->fetch_assoc() : null;

// Get users with 2FA enabled
$users_2fa_query = "SELECT a.id, a.username, a.first_name, a.last_name, a.email, 
                           uss.google_2fa_enabled, uss.google_2fa_secret, uss.created_at as setup_date
                    FROM accounts a 
                    LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
                    WHERE a.is_admin = 0 AND uss.google_2fa_enabled = 1
                    ORDER BY a.first_name, a.last_name";
$users_result = $db->query($users_2fa_query);
$users_with_2fa = [];
if ($users_result) {
    while ($row = $users_result->fetch_assoc()) {
        $users_with_2fa[] = $row;
    }
}

// Get all users for reset dropdown
$all_users_query = "SELECT id, username, first_name, last_name, email FROM accounts WHERE is_admin = 0 ORDER BY first_name, last_name";
$all_users_result = $db->query($all_users_query);
$all_users = [];
if ($all_users_result) {
    while ($row = $all_users_result->fetch_assoc()) {
        $all_users[] = $row;
    }
}

// Define page actions
$page_actions = [
    [
        'url' => 'google-2fa-setup.php',
        'label' => 'Setup User 2FA',
        'icon' => 'fas fa-qrcode'
    ]
];

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">2FA Configuration</li>
    </ol>
</nav>

<!-- Error Messages -->
<?php if (!empty($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Success Messages -->
<?php if (!empty($success)): ?>
<div class="alert alert-success" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<div class="row row-cards">
    <!-- 2FA Management Form -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-shield-alt me-2"></i>
                    2FA Management
                </h3>
            </div>
            <div class="card-body">
                <!-- Global 2FA Status -->
                <div class="mb-4">
                    <label class="form-label">Global 2FA Status</label>
                    <div class="form-control-plaintext">
                        <?php if ($global_settings && $global_settings['google_2fa_enabled']): ?>
                            <span class="badge bg-success-lt">
                                <i class="fas fa-check-circle me-1"></i>
                                Enabled
                            </span>
                        <?php else: ?>
                            <span class="badge bg-secondary-lt">
                                <i class="fas fa-times-circle me-1"></i>
                                Disabled
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="form-hint">Controls whether Google 2FA is available system-wide</div>
                </div>

                <!-- Global 2FA Controls -->
                <div class="mb-4">
                    <label class="form-label">Global Controls</label>
                    <div class="btn-list">
                        <?php if (!$global_settings || !$global_settings['google_2fa_enabled']): ?>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="enable_global_2fa">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    Enable Global 2FA
                                </button>
                            </form>
                        <?php else: ?>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="disable_global_2fa">
                                <button type="submit" class="btn btn-warning" onclick="return confirm('Are you sure you want to disable global 2FA?')">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    Disable Global 2FA
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Reset User 2FA -->
                <div class="mb-3">
                    <label class="form-label required">Reset User 2FA</label>
                    <form method="POST">
                        <input type="hidden" name="action" value="reset_user_2fa">
                        <div class="mb-3">
                            <select name="user_id" class="form-select" required>
                                <option value="">Choose a user to reset...</option>
                                <?php foreach ($all_users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>">
                                        <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name'] . ' (' . $user['username'] . ')'); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-hint">Select user to reset their Google 2FA settings</div>
                        </div>

                        <div class="form-footer">
                            <button type="submit" class="btn btn-danger" onclick="return confirm('Are you sure you want to reset this user\'s 2FA? They will need to set it up again.')">
                                <i class="fas fa-sync-alt me-2"></i>
                                Reset User 2FA
                            </button>
                            <a href="google-2fa-setup.php" class="btn btn-primary ms-2">
                                <i class="fas fa-qrcode me-2"></i>
                                Setup New 2FA
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Guidelines -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-info-circle me-2"></i>
                    2FA Guidelines
                </h3>
            </div>
            <div class="card-body">
                <!-- 2FA Types -->
                <div class="row mb-3">
                    <div class="col-6">
                        <div class="card card-sm bg-primary-lt">
                            <div class="card-body text-center">
                                <i class="fas fa-envelope text-primary mb-2" style="font-size: 1.5rem;"></i>
                                <h4 class="card-title">Email OTP</h4>
                                <p class="text-muted mb-0">6-digit codes via email</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card card-sm bg-success-lt">
                            <div class="card-body text-center">
                                <i class="fas fa-mobile-alt text-success mb-2" style="font-size: 1.5rem;"></i>
                                <h4 class="card-title">Google 2FA</h4>
                                <p class="text-muted mb-0">Authenticator app codes</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Security Rules -->
                <div class="alert alert-info mb-3">
                    <h4 class="alert-title">Security Best Practices</h4>
                    <div class="row">
                        <div class="col-6">
                            <ul class="mb-0" style="font-size: 0.9rem;">
                                <li>Verify user identity</li>
                                <li>Use secure backup codes</li>
                                <li>Regular security audits</li>
                            </ul>
                        </div>
                        <div class="col-6">
                            <ul class="mb-0" style="font-size: 0.9rem;">
                                <li>Monitor failed attempts</li>
                                <li>Document all changes</li>
                                <li>Train users properly</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Implementation Steps -->
                <div class="alert alert-warning mb-3">
                    <h4 class="alert-title">Setup Process</h4>
                    <div style="font-size: 0.9rem;">
                        <strong>1.</strong> Enable global 2FA<br>
                        <strong>2.</strong> Generate QR code for user<br>
                        <strong>3.</strong> User scans with authenticator app<br>
                        <strong>4.</strong> Verify setup with test code
                    </div>
                </div>

                <!-- Support Information -->
                <div class="alert alert-secondary mb-0">
                    <h4 class="alert-title">User Support</h4>
                    <div style="font-size: 0.85rem;">
                        <strong>Apps:</strong> Google Authenticator, Authy, Microsoft Authenticator<br>
                        <strong>Backup:</strong> Always provide backup codes<br>
                        <strong>Reset:</strong> Use admin reset for lost devices
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Users with 2FA Enabled -->
<?php if (!empty($users_with_2fa)): ?>
<div class="row row-cards mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-users me-2"></i>
                    Users with 2FA Enabled (<?php echo count($users_with_2fa); ?>)
                </h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-vcenter">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Email</th>
                                <th>Setup Date</th>
                                <th>Status</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users_with_2fa as $user): ?>
                                <tr>
                                    <td>
                                        <div class="d-flex py-1 align-items-center">
                                            <span class="avatar avatar-sm me-2"><?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?></span>
                                            <div class="flex-fill">
                                                <div class="font-weight-medium"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                                                <div class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td>
                                        <div class="text-muted"><?php echo date('M j, Y', strtotime($user['setup_date'])); ?></div>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($user['setup_date'])); ?></small>
                                    </td>
                                    <td><span class="badge bg-success-lt">Active</span></td>
                                    <td>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="action" value="reset_user_2fa">
                                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Reset 2FA for this user?')" title="Reset 2FA">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php include 'includes/admin-footer.php'; ?>

<!DOCTYPE html>
<html>
<head>
    <title>Test Delete User Functionality</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin-top: 10px; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .error { background: #ffe6e6; border: 1px solid #ff9999; }
        .success { background: #e6ffe6; border: 1px solid #99ff99; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>🔧 Delete User Functionality Tests</h1>

    <div class="test-section">
        <h3>Test 1: Session Extension</h3>
        <button onclick="testSessionExtension()">Test Session Extension</button>
        <div id="sessionResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Secure Delete User (Soft Delete)</h3>
        <button onclick="testSecureDelete()">Test Soft Delete User ID 8</button>
        <div id="secureResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Simple Delete User (New System)</h3>
        <button onclick="testSimpleDelete()">Test Simple Delete User ID 8</button>
        <div id="simpleResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 4: Delete User with High Balance</h3>
        <button onclick="testDeleteHighBalance()">Test Delete User ID 2 (John Doe - $5,000)</button>
        <div id="highBalanceResult" class="result"></div>
    </div>

    <script>
    function testSessionExtension() {
        const resultDiv = document.getElementById('sessionResult');
        resultDiv.innerHTML = '<i>Testing session extension...</i>';

        fetch('../auth/extend-session.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
            resultDiv.innerHTML =
                '<h4>Session Extension Result:</h4>' +
                '<p><strong>Success:</strong> ' + data.success + '</p>' +
                '<p><strong>Message:</strong> ' + data.message + '</p>' +
                '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            resultDiv.className = 'result error';
            resultDiv.innerHTML =
                '<h4>Session Extension Error:</h4>' +
                '<p>' + error.message + '</p>';
        });
    }

    function testSecureDelete() {
        const resultDiv = document.getElementById('secureResult');
        resultDiv.innerHTML = '<i>Testing secure delete...</i>';

        const formData = new URLSearchParams();
        formData.append('action', 'soft_delete');
        formData.append('id', '8');
        formData.append('reason', 'Test deletion via automated test');

        fetch('../admin/secure-delete-user.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
            resultDiv.innerHTML =
                '<h4>Secure Delete Result:</h4>' +
                '<p><strong>Success:</strong> ' + data.success + '</p>' +
                '<p><strong>Message:</strong> ' + data.message + '</p>' +
                '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            resultDiv.className = 'result error';
            resultDiv.innerHTML =
                '<h4>Secure Delete Error:</h4>' +
                '<p>' + error.message + '</p>';
        });
    }

    function testSimpleDelete() {
        const resultDiv = document.getElementById('simpleResult');
        resultDiv.innerHTML = '<i>Testing simple delete...</i>';

        fetch(`../admin/delete-user.php?id=8&ajax=1`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
            resultDiv.innerHTML =
                '<h4>Simple Delete Result:</h4>' +
                '<p><strong>Success:</strong> ' + data.success + '</p>' +
                '<p><strong>Message:</strong> ' + data.message + '</p>' +
                '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
        })
        .catch(error => {
            resultDiv.className = 'result error';
            resultDiv.innerHTML =
                '<h4>Simple Delete Error:</h4>' +
                '<p>' + error.message + '</p>';
        });
    }

    function testDeleteHighBalance() {
        const resultDiv = document.getElementById('highBalanceResult');
        resultDiv.innerHTML = '<i>Testing delete user with high balance...</i>';

        fetch(`../admin/delete-user.php?id=2&ajax=1`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            resultDiv.className = 'result ' + (data.success ? 'success' : 'error');
            resultDiv.innerHTML =
                '<h4>High Balance Delete Result:</h4>' +
                '<p><strong>Success:</strong> ' + data.success + '</p>' +
                '<p><strong>Message:</strong> ' + data.message + '</p>' +
                '<pre>' + JSON.stringify(data, null, 2) + '</pre>' +
                (data.success ? '<p style="color: green;">✅ Admin can delete users with any balance!</p>' :
                               '<p style="color: red;">❌ Balance restriction still exists</p>');
        })
        .catch(error => {
            resultDiv.className = 'result error';
            resultDiv.innerHTML =
                '<h4>High Balance Delete Error:</h4>' +
                '<p>' + error.message + '</p>';
        });
    }
    </script>
</body>
</html>

<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Separate Login Testing';

include '../includes/admin_header.php';
?>

<div class="page-wrapper">
    <!-- Page header -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <div class="page-pretitle">
                        Administration
                    </div>
                    <h2 class="page-title">
                        Separate Login System Testing
                    </h2>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Page body -->
    <div class="page-body">
        <div class="container-xl">
            <div class="row row-cards">
                <!-- Separation Status -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">✅ Login Separation Completed</h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-success">
                                <h4>🎉 Admin and User Login Pages Are Now Completely Separate!</h4>
                                <p>The system now has two distinct login systems with proper security separation.</p>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card bg-blue-lt">
                                        <div class="card-body text-center">
                                            <h4 class="card-title">👤 User Login</h4>
                                            <p class="card-text">
                                                <strong>URL:</strong> <code>/auth/login.php</code><br>
                                                <strong>Access:</strong> Regular customers only<br>
                                                <strong>Redirects to:</strong> User Dashboard<br>
                                                <strong>Demo:</strong> john_doe / user123
                                            </p>
                                            <a href="../auth/login.php" class="btn btn-blue" target="_blank">
                                                <i class="fas fa-user me-2"></i>
                                                Open User Login
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="card bg-red-lt">
                                        <div class="card-body text-center">
                                            <h4 class="card-title">🔐 Admin Login</h4>
                                            <p class="card-text">
                                                <strong>URL:</strong> <code>/admin/login.php</code><br>
                                                <strong>Access:</strong> Administrators only<br>
                                                <strong>Redirects to:</strong> Admin Dashboard<br>
                                                <strong>Demo:</strong> admin / admin123
                                            </p>
                                            <a href="login.php" class="btn btn-red" target="_blank">
                                                <i class="fas fa-shield-alt me-2"></i>
                                                Open Admin Login
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Changes Made -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">🔧 Changes Made</h3>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-vcenter">
                                    <thead>
                                        <tr>
                                            <th>Component</th>
                                            <th>Change</th>
                                            <th>Result</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><strong>User Login Page</strong></td>
                                            <td>Added <code>AND is_admin = 0</code> to SQL query</td>
                                            <td>Admins cannot login through user page</td>
                                            <td><span class="badge bg-green">✅ Fixed</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Admin Login Page</strong></td>
                                            <td>Separate page with admin-only authentication</td>
                                            <td>Only admins can login through admin page</td>
                                            <td><span class="badge bg-green">✅ Active</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>requireAdmin() Function</strong></td>
                                            <td>Redirects to <code>admin/login.php</code></td>
                                            <td>Unauthorized access goes to admin login</td>
                                            <td><span class="badge bg-green">✅ Fixed</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Admin Pages</strong></td>
                                            <td>Use <code>requireAdmin()</code> function</td>
                                            <td>Consistent admin authentication</td>
                                            <td><span class="badge bg-green">✅ Updated</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>User Login UI</strong></td>
                                            <td>Changed title to "Customer Login"</td>
                                            <td>Clear distinction from admin login</td>
                                            <td><span class="badge bg-green">✅ Updated</span></td>
                                        </tr>
                                        <tr>
                                            <td><strong>Demo Credentials</strong></td>
                                            <td>Separated admin and user credentials</td>
                                            <td>Each page shows relevant credentials</td>
                                            <td><span class="badge bg-green">✅ Updated</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Security Features -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">🔒 Security Features</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h5>User Login Security</h5>
                                    <ul class="list-unstyled">
                                        <li>✅ Only non-admin users can login</li>
                                        <li>✅ Admins are blocked from user login</li>
                                        <li>✅ Redirects to user dashboard only</li>
                                        <li>✅ Failed login attempts tracked</li>
                                        <li>✅ Session timeout protection</li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h5>Admin Login Security</h5>
                                    <ul class="list-unstyled">
                                        <li>✅ Only admin users can login</li>
                                        <li>✅ Separate authentication system</li>
                                        <li>✅ Redirects to admin dashboard only</li>
                                        <li>✅ Admin activity logging</li>
                                        <li>✅ Enhanced security styling</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Testing Instructions -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">🧪 Testing Instructions</h3>
                        </div>
                        <div class="card-body">
                            <div class="steps">
                                <div class="step-item">
                                    <div class="h4">1. Test User Login Separation</div>
                                    <p>Navigate to <code>http://localhost/online_banking/auth/login.php</code></p>
                                    <ul>
                                        <li>Try to login with admin credentials: <strong>admin / admin123</strong></li>
                                        <li>Should show "Invalid username or password" error</li>
                                        <li>Login with user credentials: <strong>john_doe / user123</strong></li>
                                        <li>Should redirect to user dashboard successfully</li>
                                    </ul>
                                </div>
                                
                                <div class="step-item">
                                    <div class="h4">2. Test Admin Login Separation</div>
                                    <p>Navigate to <code>http://localhost/online_banking/admin/login.php</code></p>
                                    <ul>
                                        <li>Try to login with user credentials: <strong>john_doe / user123</strong></li>
                                        <li>Should show "Invalid username or password" error</li>
                                        <li>Login with admin credentials: <strong>admin / admin123</strong></li>
                                        <li>Should redirect to admin dashboard successfully</li>
                                    </ul>
                                </div>
                                
                                <div class="step-item">
                                    <div class="h4">3. Test Unauthorized Access</div>
                                    <p>Test admin page protection</p>
                                    <ul>
                                        <li>Logout completely</li>
                                        <li>Try to access: <code>http://localhost/online_banking/admin/users/</code></li>
                                        <li>Should redirect to admin login page (not user login)</li>
                                        <li>Login as user and try again</li>
                                        <li>Should still redirect to admin login page</li>
                                    </ul>
                                </div>
                                
                                <div class="step-item">
                                    <div class="h4">4. Test Cross-Login Links</div>
                                    <p>Verify navigation between login pages</p>
                                    <ul>
                                        <li>From user login page, click "Admin Login" link</li>
                                        <li>Should navigate to admin login page</li>
                                        <li>From admin login page, click "Back to Main Site"</li>
                                        <li>Should navigate to main site (which redirects to user login)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Test Links -->
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">🔗 Quick Test Links</h3>
                        </div>
                        <div class="card-body">
                            <div class="btn-list">
                                <a href="logout.php" class="btn btn-outline-danger">
                                    <i class="fas fa-sign-out-alt me-2"></i>
                                    Logout & Test Admin Login
                                </a>
                                <a href="../auth/login.php" class="btn btn-outline-blue" target="_blank">
                                    <i class="fas fa-user me-2"></i>
                                    Open User Login Page
                                </a>
                                <a href="login.php" class="btn btn-outline-red" target="_blank">
                                    <i class="fas fa-shield-alt me-2"></i>
                                    Open Admin Login Page
                                </a>
                                <a href="../" class="btn btn-outline-primary" target="_blank">
                                    <i class="fas fa-home me-2"></i>
                                    Test Main Site Redirect
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>

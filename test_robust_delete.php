<?php
// More robust test for delete functionality
echo "<h2>Delete User Direct Test</h2><hr>";

// Set up session for admin user
session_start();
$_SESSION['user_id'] = 1; // Admin user
$_SESSION['is_admin'] = true;

echo "✅ Session initialized with admin user<br><br>";

// Change to admin directory context
$original_dir = getcwd();
chdir(__DIR__ . '/admin');

echo "✅ Changed to admin directory context<br><br>";

// Simulate the AJAX request environment
$_SERVER['REQUEST_METHOD'] = 'POST';
$_GET['id'] = '9'; // Test user ID (ajax_test_user)
$_GET['ajax'] = '1';

echo "✅ Set up request parameters: POST, id=9, ajax=1<br><br>";

// Start output buffering to capture the response
ob_start();
$error_occurred = false;

try {
    // Include the delete-user.php file
    include 'delete-user.php';
} catch (Exception $e) {
    $error_occurred = true;
    $error_message = $e->getMessage();
} catch (Error $e) {
    $error_occurred = true;
    $error_message = $e->getMessage();
}

$response = ob_get_contents();
ob_end_clean();

// Change back to original directory
chdir($original_dir);

echo "<h3>Test Results:</h3>";

if ($error_occurred) {
    echo "❌ <strong>Error occurred:</strong> " . htmlspecialchars($error_message) . "<br><br>";
}

echo "<h4>Raw Response:</h4>";
echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 300px; overflow: auto;'>";
echo htmlspecialchars($response);
echo "</pre>";

// Try to decode as JSON
if (!empty($response)) {
    $json_response = json_decode($response, true);
    if ($json_response) {
        echo "<h4>Parsed JSON Response:</h4>";
        echo "<ul>";
        echo "<li><strong>Success:</strong> " . ($json_response['success'] ? '✅ Yes' : '❌ No') . "</li>";
        echo "<li><strong>Message:</strong> " . htmlspecialchars($json_response['message']) . "</li>";
        echo "</ul>";
        
        if ($json_response['success']) {
            echo "<p>🎉 <strong>Delete operation completed successfully!</strong></p>";
        } else {
            echo "<p>⚠️ <strong>Delete operation failed.</strong></p>";
        }
    } else {
        echo "<h4>Response Analysis:</h4>";
        echo "<p>Response is not valid JSON. This might indicate an error or redirect.</p>";
    }
} else {
    echo "<h4>Response Analysis:</h4>";
    echo "<p>No response captured. This might indicate a redirect or exit was called.</p>";
}
?>

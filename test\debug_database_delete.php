<?php
require_once '../config/config.php';

echo "<h2>🔍 Database Delete Debug</h2>";

try {
    $db = getDB();
    $user_id = 6;
    
    // First, let's check if the user exists
    echo "<h3>1. Checking if user exists:</h3>";
    $check_query = "SELECT id, username, email, is_admin FROM accounts WHERE id = ?";
    $result = $db->query($check_query, [$user_id]);
    
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        echo "✅ User found:<br>";
        echo "- ID: {$user['id']}<br>";
        echo "- Username: {$user['username']}<br>";
        echo "- Email: {$user['email']}<br>";
        echo "- Is Admin: " . ($user['is_admin'] ? 'Yes' : 'No') . "<br>";
    } else {
        echo "❌ User with ID $user_id not found!<br>";
    }
    
    // Test the exact delete query
    echo "<h3>2. Testing delete query conditions:</h3>";
    
    // Check if user is admin
    $admin_check = "SELECT id FROM accounts WHERE id = ? AND is_admin = 1";
    $admin_result = $db->query($admin_check, [$user_id]);
    if ($admin_result->num_rows > 0) {
        echo "❌ User is an admin - cannot delete<br>";
    } else {
        echo "✅ User is not admin - can delete<br>";
    }
    
    // Check the exact condition
    $condition_check = "SELECT id FROM accounts WHERE id = ? AND is_admin = 0";
    $condition_result = $db->query($condition_check, [$user_id]);
    if ($condition_result->num_rows > 0) {
        echo "✅ User matches delete condition (id = $user_id AND is_admin = 0)<br>";
    } else {
        echo "❌ User does NOT match delete condition<br>";
    }
    
    // Test the Database class delete method vs direct query
    echo "<h3>3. Testing different delete approaches:</h3>";
    
    // Method 1: Using Database->delete() method
    echo "<strong>Method 1: Database->delete() method</strong><br>";
    try {
        // Start transaction for testing
        $db->query("START TRANSACTION");
        
        $affected = $db->delete("DELETE FROM accounts WHERE id = ? AND is_admin = 0", [$user_id]);
        echo "Result: $affected affected rows<br>";
        
        // Rollback to not actually delete
        $db->query("ROLLBACK");
        echo "Transaction rolled back (test only)<br>";
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        echo "Error: " . $e->getMessage() . "<br>";
    }
    
    // Method 2: Using Database->query() method
    echo "<br><strong>Method 2: Database->query() method</strong><br>";
    try {
        // Start transaction for testing
        $db->query("START TRANSACTION");
        
        $result = $db->query("DELETE FROM accounts WHERE id = ? AND is_admin = 0", [$user_id]);
        
        // Check affected rows using mysqli connection directly
        $affected = $db->connection->affected_rows;
        echo "Query result: " . ($result ? 'Success' : 'Failed') . "<br>";
        echo "Affected rows: $affected<br>";
        
        // Rollback to not actually delete
        $db->query("ROLLBACK");
        echo "Transaction rolled back (test only)<br>";
        
    } catch (Exception $e) {
        $db->query("ROLLBACK");
        echo "Error: " . $e->getMessage() . "<br>";
    }
    
    // Method 3: Direct mysqli query (for comparison)
    echo "<br><strong>Method 3: Direct mysqli query</strong><br>";
    try {
        // Get the raw connection
        $mysqli = $db->connection;
        
        // Start transaction
        $mysqli->autocommit(false);
        
        // Prepare and execute
        $stmt = $mysqli->prepare("DELETE FROM accounts WHERE id = ? AND is_admin = 0");
        $stmt->bind_param("i", $user_id);
        $result = $stmt->execute();
        
        echo "Execute result: " . ($result ? 'Success' : 'Failed') . "<br>";
        echo "Affected rows: " . $mysqli->affected_rows . "<br>";
        echo "Error (if any): " . $mysqli->error . "<br>";
        
        // Rollback
        $mysqli->rollback();
        $mysqli->autocommit(true);
        echo "Transaction rolled back (test only)<br>";
        
        $stmt->close();
        
    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "<br>";
    }
    
    // Check Database class implementation
    echo "<h3>4. Checking Database class delete method:</h3>";
    
    // Let's look at what the delete method actually does
    echo "<p>The Database->delete() method should:</p>";
    echo "<ol>";
    echo "<li>Call query() with the SQL and parameters</li>";
    echo "<li>Return \$this->connection->affected_rows</li>";
    echo "</ol>";
    
    // Test if there are any foreign key constraints
    echo "<h3>5. Checking for foreign key constraints:</h3>";
    try {
        $fk_query = "SELECT 
            CONSTRAINT_NAME,
            TABLE_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE REFERENCED_TABLE_NAME = 'accounts' 
        AND TABLE_SCHEMA = DATABASE()";
        
        $fk_result = $db->query($fk_query);
        
        if ($fk_result->num_rows > 0) {
            echo "⚠️ Foreign key constraints found:<br>";
            while ($fk = $fk_result->fetch_assoc()) {
                echo "- {$fk['TABLE_NAME']}.{$fk['COLUMN_NAME']} → {$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}<br>";
            }
        } else {
            echo "✅ No foreign key constraints found<br>";
        }
    } catch (Exception $e) {
        echo "Could not check foreign keys: " . $e->getMessage() . "<br>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ Error!</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
h2, h3 { color: #333; }
ol, ul { padding-left: 20px; }
li { margin: 5px 0; }
</style>

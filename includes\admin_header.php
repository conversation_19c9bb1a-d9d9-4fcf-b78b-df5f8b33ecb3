<?php
/**
 * Admin Header - Administrative interface header with full admin functionality
 * This header is only for admin users and contains all administrative features
 */

// Ensure user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('auth/login.php');
}

// Get appearance settings
$appearance = getAppearanceSettings();
$bank_name = getBankName();
$theme = $appearance['theme'] ?? 'light';
$color_scheme = $appearance['color_scheme'] ?? 'blue';
$header_fixed = ($appearance['header_fixed'] ?? 'true') === 'true';
$animations_enabled = ($appearance['animations_enabled'] ?? 'true') === 'true';

// Get admin information
$admin_name = $_SESSION['first_name'] . ' ' . $_SESSION['last_name'];
$admin_email = $_SESSION['email'] ?? '';
$admin_avatar = strtoupper(substr($_SESSION['first_name'], 0, 1) . substr($_SESSION['last_name'], 0, 1));
?>
<!DOCTYPE html>
<html lang="en" data-theme="<?php echo htmlspecialchars($theme); ?>" data-color-scheme="<?php echo htmlspecialchars($color_scheme); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) . ' - Admin - ' : 'Admin - '; ?><?php echo htmlspecialchars($bank_name); ?></title>
    
    <!-- Favicon -->
    <?php if (!empty($appearance['favicon_url'])): ?>
    <link rel="icon" type="image/x-icon" href="<?php echo htmlspecialchars($appearance['favicon_url']); ?>">
    <?php else: ?>
    <link rel="icon" type="image/x-icon" href="<?php echo asset('favicon.ico'); ?>">
    <?php endif; ?>
    
    <!-- CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.3.2/dist/css/tabler.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons@1.39.1/icons-sprite.svg" rel="preload" as="image">
    
    <!-- Font Awesome for additional icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js for admin dashboards -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- Bootstrap JavaScript for dropdowns and components -->
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.3.2/dist/js/tabler.min.js"></script>

    <!-- Custom admin styles -->
    <style>
        :root {
            --admin-primary: #dc3545;
            --admin-secondary: #c82333;
            --bank-primary: <?php echo $color_scheme === 'blue' ? '#0054a6' : ($color_scheme === 'green' ? '#2e7d32' : '#d32f2f'); ?>;
            --bank-secondary: <?php echo $color_scheme === 'blue' ? '#0066cc' : ($color_scheme === 'green' ? '#388e3c' : '#f44336'); ?>;
        }
        
        <?php if ($theme === 'dark'): ?>
        .navbar-dark {
            background-color: #1a1a1a !important;
            border-bottom: 1px solid #333;
        }
        
        .dropdown-menu {
            background-color: #2d2d2d;
            border-color: #444;
        }
        
        .dropdown-item {
            color: #e0e0e0;
        }
        
        .dropdown-item:hover {
            background-color: #404040;
            color: #fff;
        }
        <?php endif; ?>
        
        .navbar-brand {
            font-weight: 600;
            color: var(--bank-primary) !important;
        }
        
        .admin-badge {
            background: var(--admin-primary);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 3px;
            margin-left: 8px;
            font-weight: 600;
        }
        
        .bank-logo {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--bank-primary) 0%, var(--bank-secondary) 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
        }
        
        <?php if (!$animations_enabled): ?>
        * {
            animation: none !important;
            transition: none !important;
        }
        <?php endif; ?>
        
        .admin-avatar {
            width: 32px;
            height: 32px;
            background: var(--admin-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            border: 2px solid var(--admin-secondary);
        }
        
        .navbar-nav .nav-link {
            color: #495057 !important;
            font-weight: 500;
        }
        
        .navbar-nav .nav-link:hover {
            color: var(--admin-primary) !important;
        }
        
        .dropdown-toggle::after {
            display: none;
        }
        
        .admin-nav-item {
            /* Clean navigation without thick borders */
            padding: 0.5rem 0.75rem !important;
        }

        /* Mobile responsive improvements */
        @media (max-width: 991.98px) {
            .navbar-nav .nav-link {
                padding: 0.5rem 1rem !important;
                border-bottom: 1px solid rgba(255,255,255,0.1);
            }

            .navbar-nav .nav-link:last-child {
                border-bottom: none;
            }

            .dropdown-menu {
                border: none;
                box-shadow: none;
                background: rgba(255,255,255,0.1);
                margin-left: 1rem;
            }

            .dropdown-item {
                color: rgba(255,255,255,0.8) !important;
                padding: 0.5rem 1rem;
            }

            .dropdown-item:hover {
                background: rgba(255,255,255,0.1);
                color: white !important;
            }
        }

        /* Compact header on small screens */
        @media (max-width: 767.98px) {
            .navbar-brand {
                font-size: 1rem !important;
            }

            .admin-alert {
                font-size: 0.875rem;
                padding: 0.5rem 0;
            }

            .admin-alert .btn {
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
            }
        }
        
        .quick-actions {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
            border-radius: 8px;
            padding: 0.5rem;
        }
        
        .quick-actions a {
            color: white !important;
            text-decoration: none;
            font-size: 12px;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .quick-actions a:hover {
            background-color: rgba(255,255,255,0.2);
        }
    </style>
</head>
<body class="<?php echo $theme === 'dark' ? 'theme-dark' : ''; ?>">
    
    <!-- Navigation Header -->
    <header class="navbar navbar-expand-md navbar-light d-print-none <?php echo $header_fixed ? 'navbar-sticky' : ''; ?>">
        <div class="container-xl">
            <!-- Brand -->
            <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
                <a href="<?php echo url('admin/'); ?>" class="d-flex align-items-center text-decoration-none">
                    <?php if (!empty($appearance['logo_url'])): ?>
                        <img src="<?php echo htmlspecialchars($appearance['logo_url']); ?>" alt="<?php echo htmlspecialchars($bank_name); ?>" class="bank-logo" style="background: none;">
                    <?php else: ?>
                        <div class="bank-logo">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <path d="M3 21l18 0"/>
                                <path d="M3 10l18 0"/>
                                <path d="M5 6l7 -3l7 3"/>
                                <path d="M4 10l0 11"/>
                                <path d="M20 10l0 11"/>
                                <path d="M8 14l0 3"/>
                                <path d="M12 14l0 3"/>
                                <path d="M16 14l0 3"/>
                            </svg>
                        </div>
                    <?php endif; ?>
                    <?php echo htmlspecialchars($bank_name); ?>
                    <span class="admin-badge">ADMIN</span>
                </a>
            </h1>
            
            <!-- Mobile menu toggle -->
            <div class="navbar-nav flex-row order-md-last">
                <!-- Quick Actions -->
                <div class="nav-item dropdown me-3">
                    <a href="#" class="nav-link" data-bs-toggle="dropdown" aria-label="Quick actions">
                        <i class="fas fa-bolt text-warning"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow quick-actions">
                        <div class="d-flex flex-column gap-1">
                            <a href="<?php echo url('admin/add-user.php'); ?>">
                                <i class="fas fa-user-plus me-1"></i> Add User
                            </a>
                            <a href="<?php echo url('admin/virtual-cards/'); ?>">
                                <i class="fas fa-credit-card me-1"></i> Virtual Cards
                            </a>
                            <a href="<?php echo url('admin/settings/'); ?>">
                                <i class="fas fa-cogs me-1"></i> Settings
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Admin Menu -->
                <div class="nav-item dropdown">
                    <a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown" aria-label="Open admin menu">
                        <div class="admin-avatar">
                            <?php echo strtoupper(substr($admin_name, 0, 1)); ?>
                        </div>
                        <div class="d-none d-xl-block ps-2">
                            <div><?php echo htmlspecialchars($admin_name); ?></div>
                            <div class="mt-1 small text-muted">Administrator</div>
                        </div>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
                        <a href="<?php echo url('admin/settings/'); ?>" class="dropdown-item">
                            <i class="fas fa-cogs me-2"></i>
                            System Settings
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="<?php echo url('dashboard/'); ?>" class="dropdown-item">
                            <i class="fas fa-eye me-2"></i>
                            View as User
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="<?php echo url('admin/logout.php'); ?>" class="dropdown-item text-danger">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Navigation Menu - Mobile Responsive -->
            <div class="collapse navbar-collapse" id="navbar-menu">
                <div class="d-flex flex-column flex-md-row flex-fill align-items-stretch align-items-md-center">
                    <!-- Main Navigation -->
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link admin-nav-item" href="<?php echo url('admin/'); ?>">
                                <i class="fas fa-tachometer-alt me-1"></i>
                                <span class="d-none d-lg-inline">Dashboard</span>
                                <span class="d-lg-none">Home</span>
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle admin-nav-item" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-users me-1"></i>
                                <span class="d-none d-lg-inline">Users</span>
                                <span class="d-lg-none">Users</span>
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="<?php echo url('admin/users.php'); ?>">
                                    <i class="fas fa-list me-2"></i>All Users
                                </a>
                                <a class="dropdown-item" href="<?php echo url('admin/add-user.php'); ?>">
                                    <i class="fas fa-user-plus me-2"></i>Add User
                                </a>
                            </div>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link admin-nav-item" href="<?php echo url('admin/accounts/'); ?>">
                                <i class="fas fa-university me-1"></i>
                                <span class="d-none d-lg-inline">Accounts</span>
                                <span class="d-lg-none">Accounts</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link admin-nav-item" href="<?php echo url('admin/virtual-cards/'); ?>">
                                <i class="fas fa-credit-card me-1"></i>
                                <span class="d-none d-lg-inline">Cards</span>
                                <span class="d-lg-none">Cards</span>
                            </a>
                        </li>
                        <li class="nav-item d-lg-none">
                            <a class="nav-link admin-nav-item" href="<?php echo url('admin/settings/'); ?>">
                                <i class="fas fa-cogs me-1"></i>
                                Settings
                            </a>
                        </li>
                    </ul>

                    <!-- Desktop Settings (hidden on mobile) -->
                    <ul class="navbar-nav d-none d-lg-flex">
                        <li class="nav-item">
                            <a class="nav-link admin-nav-item" href="<?php echo url('admin/settings/'); ?>">
                                <i class="fas fa-cogs me-1"></i>
                                Settings
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </header>
    
    <!-- Flash Messages -->
    <?php if (hasFlashMessage('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars(getFlashMessage('success')); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (hasFlashMessage('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars(getFlashMessage('error')); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (hasFlashMessage('warning')): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars(getFlashMessage('warning')); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <!-- Admin Alert Banner -->
    <div class="alert alert-info border-0 rounded-0 mb-0 admin-alert" role="alert">
        <div class="container-xl">
            <div class="d-flex align-items-center flex-wrap">
                <i class="fas fa-shield-alt me-2"></i>
                <span class="flex-grow-1">
                    <strong>Administrator Mode:</strong>
                    <span class="d-none d-md-inline">You are currently logged in as an administrator with full system access.</span>
                    <span class="d-md-none">Admin access enabled.</span>
                </span>
                <a href="<?php echo url('dashboard/'); ?>" class="btn btn-sm btn-outline-info ms-auto">
                    <i class="fas fa-eye me-1"></i>
                    <span class="d-none d-sm-inline">Switch to User View</span>
                    <span class="d-sm-none">User View</span>
                </a>
            </div>
        </div>
    </div>
    
    <!-- Main Content Container -->
    <div class="page-wrapper">
        <div class="page-body">
            <div class="container-xl">

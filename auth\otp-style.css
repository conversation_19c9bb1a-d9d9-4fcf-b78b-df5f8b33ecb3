/* OTP Verification Page Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Aria<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.otp-container {
    width: 100%;
    max-width: 480px;
}

.otp-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 3rem 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.otp-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
}

.otp-header {
    margin-bottom: 2rem;
}

.otp-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 8px 24px rgba(99, 102, 241, 0.3);
}

.otp-logo i {
    font-size: 2rem;
    color: white;
}

.otp-title {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.otp-subtitle {
    color: #6b7280;
    font-size: 1rem;
    line-height: 1.5;
    max-width: 400px;
    margin: 0 auto;
}

/* Alert Styles */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.alert-error {
    background: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
}

.alert-success {
    background: #f0fdf4;
    color: #16a34a;
    border: 1px solid #bbf7d0;
}

.alert-warning {
    background: #fffbeb;
    color: #d97706;
    border: 1px solid #fed7aa;
}

/* OTP Form */
.otp-form {
    margin-bottom: 2rem;
}

.otp-input-group {
    margin-bottom: 2rem;
}

.otp-label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 1rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.otp-inputs {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
    margin-bottom: 1rem;
}

.otp-input {
    width: 50px;
    height: 60px;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    text-align: center;
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    background: #f9fafb;
    transition: all 0.2s ease;
    outline: none;
}

.otp-input:focus {
    border-color: #6366f1;
    background: white;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    transform: scale(1.05);
}

.otp-input:valid {
    border-color: #10b981;
    background: #f0fdf4;
}

/* Verify Button */
.verify-button {
    width: 100%;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 1rem 2rem;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    opacity: 0.6;
    transform: scale(0.98);
}

.verify-button:not(:disabled) {
    opacity: 1;
    transform: scale(1);
}

.verify-button:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(99, 102, 241, 0.4);
}

.verify-button.ready {
    background: linear-gradient(135deg, #10b981, #059669);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.02); }
}

.verify-button:disabled {
    cursor: not-allowed;
}

/* OTP Actions */
.otp-actions {
    margin-bottom: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.resend-text {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.resend-link {
    color: #6366f1;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.resend-link:hover {
    color: #4f46e5;
    transform: translateY(-1px);
}

/* Back to Login */
.back-to-login {
    padding-top: 1rem;
    border-top: 1px solid #e5e7eb;
}

.back-to-login a {
    color: #6b7280;
    text-decoration: none;
    font-size: 0.875rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.back-to-login a:hover {
    color: #374151;
    transform: translateX(-2px);
}

/* Responsive Design */
@media (max-width: 640px) {
    .otp-card {
        padding: 2rem 1.5rem;
        margin: 1rem;
    }
    
    .otp-inputs {
        gap: 0.5rem;
    }
    
    .otp-input {
        width: 45px;
        height: 55px;
        font-size: 1.25rem;
    }
    
    .otp-title {
        font-size: 1.5rem;
    }
    
    .otp-subtitle {
        font-size: 0.875rem;
    }
}

/* Loading Animation */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Success Animation */
@keyframes checkmark {
    0% { transform: scale(0) rotate(45deg); }
    50% { transform: scale(1.2) rotate(45deg); }
    100% { transform: scale(1) rotate(45deg); }
}

.success-checkmark {
    animation: checkmark 0.6s ease-in-out;
}

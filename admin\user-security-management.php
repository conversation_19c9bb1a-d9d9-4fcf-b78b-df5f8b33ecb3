<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'User Security Management';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'update_user_security') {
        try {
            $db = getDB();
            
            $user_id = intval($_POST['user_id']);
            $otp_enabled = isset($_POST['otp_enabled']) ? 1 : 0;
            $google_2fa_enabled = isset($_POST['google_2fa_enabled']) ? 1 : 0;
            $require_2fa = isset($_POST['require_2fa']) ? 1 : 0;
            $allow_remember_device = isset($_POST['allow_remember_device']) ? 1 : 0;
            $login_attempts_limit = intval($_POST['login_attempts_limit']);
            $lockout_duration = intval($_POST['lockout_duration']);
            $otp_expiry_minutes = intval($_POST['otp_expiry_minutes']);
            $reason = trim($_POST['reason'] ?? '');
            
            // Validate inputs
            if ($user_id <= 0) {
                throw new Exception("Invalid user ID.");
            }
            
            if ($login_attempts_limit < 1 || $login_attempts_limit > 20) {
                throw new Exception("Login attempts limit must be between 1 and 20.");
            }
            
            if ($lockout_duration < 1 || $lockout_duration > 1440) {
                throw new Exception("Lockout duration must be between 1 and 1440 minutes.");
            }
            
            if ($otp_expiry_minutes < 1 || $otp_expiry_minutes > 60) {
                throw new Exception("OTP expiry must be between 1 and 60 minutes.");
            }
            
            // Get current settings for history
            $current_query = "SELECT * FROM user_security_settings WHERE user_id = ?";
            $current_result = $db->query($current_query, [$user_id]);
            $current_settings = $current_result ? $current_result->fetch_assoc() : null;
            
            if (!$current_settings) {
                throw new Exception("User security settings not found.");
            }
            
            // Update user security settings
            $update_security = "UPDATE user_security_settings SET 
                               otp_enabled = ?, google_2fa_enabled = ?, require_2fa = ?, 
                               allow_remember_device = ?, login_attempts_limit = ?, 
                               lockout_duration = ?, otp_expiry_minutes = ?, updated_by = ?
                               WHERE user_id = ?";
            
            $update_result = $db->query($update_security, [
                $otp_enabled, $google_2fa_enabled, $require_2fa, $allow_remember_device,
                $login_attempts_limit, $lockout_duration, $otp_expiry_minutes,
                $_SESSION['user_id'], $user_id
            ]);
            
            if (!$update_result) {
                throw new Exception("Failed to update user security settings.");
            }
            
            // Log changes in history
            $changes = [];
            if ($current_settings['otp_enabled'] != $otp_enabled) {
                $changes[] = ['setting' => 'otp_enabled', 'old' => $current_settings['otp_enabled'], 'new' => $otp_enabled];
            }
            if ($current_settings['google_2fa_enabled'] != $google_2fa_enabled) {
                $changes[] = ['setting' => 'google_2fa_enabled', 'old' => $current_settings['google_2fa_enabled'], 'new' => $google_2fa_enabled];
            }
            if ($current_settings['require_2fa'] != $require_2fa) {
                $changes[] = ['setting' => 'require_2fa', 'old' => $current_settings['require_2fa'], 'new' => $require_2fa];
            }
            if ($current_settings['allow_remember_device'] != $allow_remember_device) {
                $changes[] = ['setting' => 'allow_remember_device', 'old' => $current_settings['allow_remember_device'], 'new' => $allow_remember_device];
            }
            if ($current_settings['login_attempts_limit'] != $login_attempts_limit) {
                $changes[] = ['setting' => 'login_attempts_limit', 'old' => $current_settings['login_attempts_limit'], 'new' => $login_attempts_limit];
            }
            if ($current_settings['lockout_duration'] != $lockout_duration) {
                $changes[] = ['setting' => 'lockout_duration', 'old' => $current_settings['lockout_duration'], 'new' => $lockout_duration];
            }
            if ($current_settings['otp_expiry_minutes'] != $otp_expiry_minutes) {
                $changes[] = ['setting' => 'otp_expiry_minutes', 'old' => $current_settings['otp_expiry_minutes'], 'new' => $otp_expiry_minutes];
            }
            
            // Insert history records
            foreach ($changes as $change) {
                $log_change = "INSERT INTO user_security_history (
                              user_id, setting_changed, old_value, new_value, reason, changed_by
                              ) VALUES (?, ?, ?, ?, ?, ?)";
                $db->query($log_change, [
                    $user_id, $change['setting'], $change['old'], $change['new'], $reason, $_SESSION['user_id']
                ]);
            }
            
            // Get user name for success message
            $user_query = "SELECT first_name, last_name FROM accounts WHERE id = ?";
            $user_result = $db->query($user_query, [$user_id]);
            $user_info = $user_result ? $user_result->fetch_assoc() : null;
            $user_name = $user_info ? $user_info['first_name'] . ' ' . $user_info['last_name'] : 'User';
            
            $success = "✅ Security settings updated successfully for " . $user_name . " (" . count($changes) . " changes made)";
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    } elseif (isset($_POST['action']) && $_POST['action'] === 'unlock_user') {
        try {
            $db = getDB();
            
            $user_id = intval($_POST['user_id']);
            $reason = trim($_POST['reason'] ?? 'Admin unlock');
            
            // Unlock user
            $unlock_query = "UPDATE user_security_settings SET 
                            locked_until = NULL, failed_attempts = 0, updated_by = ?
                            WHERE user_id = ?";
            $db->query($unlock_query, [$_SESSION['user_id'], $user_id]);
            
            // Log the unlock
            $log_unlock = "INSERT INTO user_security_history (
                          user_id, setting_changed, old_value, new_value, reason, changed_by
                          ) VALUES (?, ?, ?, ?, ?, ?)";
            $db->query($log_unlock, [
                $user_id, 'account_unlocked', 'locked', 'unlocked', $reason, $_SESSION['user_id']
            ]);
            
            $success = "✅ User account unlocked successfully!";
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    } elseif (isset($_POST['action']) && $_POST['action'] === 'bulk_update') {
        try {
            $db = getDB();
            
            $user_ids = $_POST['user_ids'] ?? [];
            $bulk_action = $_POST['bulk_action'];
            $reason = trim($_POST['bulk_reason'] ?? '');
            
            if (empty($user_ids)) {
                throw new Exception("No users selected.");
            }
            
            $updated_count = 0;
            
            foreach ($user_ids as $user_id) {
                $user_id = intval($user_id);
                
                switch ($bulk_action) {
                    case 'enable_otp':
                        $db->query("UPDATE user_security_settings SET otp_enabled = 1, updated_by = ? WHERE user_id = ?", 
                                  [$_SESSION['user_id'], $user_id]);
                        $db->query("INSERT INTO user_security_history (user_id, setting_changed, old_value, new_value, reason, changed_by) VALUES (?, ?, ?, ?, ?, ?)",
                                  [$user_id, 'otp_enabled', '0', '1', $reason, $_SESSION['user_id']]);
                        break;
                        
                    case 'disable_otp':
                        $db->query("UPDATE user_security_settings SET otp_enabled = 0, updated_by = ? WHERE user_id = ?", 
                                  [$_SESSION['user_id'], $user_id]);
                        $db->query("INSERT INTO user_security_history (user_id, setting_changed, old_value, new_value, reason, changed_by) VALUES (?, ?, ?, ?, ?, ?)",
                                  [$user_id, 'otp_enabled', '1', '0', $reason, $_SESSION['user_id']]);
                        break;
                        
                    case 'require_2fa':
                        $db->query("UPDATE user_security_settings SET require_2fa = 1, updated_by = ? WHERE user_id = ?", 
                                  [$_SESSION['user_id'], $user_id]);
                        $db->query("INSERT INTO user_security_history (user_id, setting_changed, old_value, new_value, reason, changed_by) VALUES (?, ?, ?, ?, ?, ?)",
                                  [$user_id, 'require_2fa', '0', '1', $reason, $_SESSION['user_id']]);
                        break;
                        
                    case 'optional_2fa':
                        $db->query("UPDATE user_security_settings SET require_2fa = 0, updated_by = ? WHERE user_id = ?", 
                                  [$_SESSION['user_id'], $user_id]);
                        $db->query("INSERT INTO user_security_history (user_id, setting_changed, old_value, new_value, reason, changed_by) VALUES (?, ?, ?, ?, ?, ?)",
                                  [$user_id, 'require_2fa', '1', '0', $reason, $_SESSION['user_id']]);
                        break;
                        
                    case 'unlock_accounts':
                        $db->query("UPDATE user_security_settings SET locked_until = NULL, failed_attempts = 0, updated_by = ? WHERE user_id = ?", 
                                  [$_SESSION['user_id'], $user_id]);
                        $db->query("INSERT INTO user_security_history (user_id, setting_changed, old_value, new_value, reason, changed_by) VALUES (?, ?, ?, ?, ?, ?)",
                                  [$user_id, 'account_unlocked', 'locked', 'unlocked', $reason, $_SESSION['user_id']]);
                        break;
                }
                $updated_count++;
            }
            
            $success = "✅ Bulk update completed successfully! Updated {$updated_count} users.";
            
        } catch (Exception $e) {
            $error = $e->getMessage();
        }
    }
}

// Pagination settings
$records_per_page = 25;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

// Filter parameters
$filter_otp = $_GET['otp'] ?? '';
$filter_2fa = $_GET['2fa'] ?? '';
$filter_locked = $_GET['locked'] ?? '';
$filter_user = $_GET['user'] ?? '';

// Build WHERE clause for filters
$where_conditions = ["a.is_admin = 0"];
$params = [];

if (!empty($filter_otp)) {
    $where_conditions[] = "uss.otp_enabled = ?";
    $params[] = $filter_otp === 'enabled' ? 1 : 0;
}

if (!empty($filter_2fa)) {
    $where_conditions[] = "uss.require_2fa = ?";
    $params[] = $filter_2fa === 'required' ? 1 : 0;
}

if (!empty($filter_locked)) {
    if ($filter_locked === 'locked') {
        $where_conditions[] = "uss.locked_until > NOW()";
    } else {
        $where_conditions[] = "(uss.locked_until IS NULL OR uss.locked_until <= NOW())";
    }
}

if (!empty($filter_user)) {
    $where_conditions[] = "(a.first_name LIKE ? OR a.last_name LIKE ? OR a.username LIKE ? OR a.email LIKE ?)";
    $search_term = "%$filter_user%";
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

$where_clause = "WHERE " . implode(" AND ", $where_conditions);

try {
    $db = getDB();
    
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total 
                    FROM accounts a 
                    LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
                    $where_clause";
    $count_result = $db->query($count_query, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);
    
    // Get users with security settings
    $users_query = "SELECT a.id, a.first_name, a.last_name, a.username, a.email, a.account_number, 
                    a.created_at, a.last_login, a.account_status,
                    uss.otp_enabled, uss.google_2fa_enabled, uss.require_2fa, uss.allow_remember_device,
                    uss.login_attempts_limit, uss.lockout_duration, uss.otp_expiry_minutes,
                    uss.failed_attempts, uss.locked_until, uss.last_login_attempt, uss.updated_at
                    FROM accounts a 
                    LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
                    $where_clause
                    ORDER BY a.created_at DESC 
                    LIMIT $records_per_page OFFSET $offset";
    
    $users_result = $db->query($users_query, $params);
    $users = [];
    if ($users_result) {
        while ($row = $users_result->fetch_assoc()) {
            $users[] = $row;
        }
    }
    
    // Get summary statistics
    $stats_query = "SELECT 
                    COUNT(*) as total_users,
                    COUNT(CASE WHEN uss.otp_enabled = 1 THEN 1 END) as otp_enabled_users,
                    COUNT(CASE WHEN uss.require_2fa = 1 THEN 1 END) as require_2fa_users,
                    COUNT(CASE WHEN uss.locked_until > NOW() THEN 1 END) as locked_users,
                    COUNT(CASE WHEN uss.google_2fa_enabled = 1 THEN 1 END) as google_2fa_users
                    FROM accounts a 
                    LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
                    WHERE a.is_admin = 0";
    $stats_result = $db->query($stats_query);
    $stats = $stats_result ? $stats_result->fetch_assoc() : ['total_users' => 0, 'otp_enabled_users' => 0, 'require_2fa_users' => 0, 'locked_users' => 0, 'google_2fa_users' => 0];
    
} catch (Exception $e) {
    $error = "Failed to load user security settings: " . $e->getMessage();
    $users = [];
    $total_records = 0;
    $total_pages = 0;
    $stats = ['total_users' => 0, 'otp_enabled_users' => 0, 'require_2fa_users' => 0, 'locked_users' => 0, 'google_2fa_users' => 0];
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">User Security Management</li>
    </ol>
</nav>

<!-- Success Message -->
<?php if (isset($success)): ?>
<div class="alert alert-success alert-dismissible" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo $success; ?></div>
        </div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Statistics Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-users"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['total_users']); ?></div>
                        <div class="text-muted">Total Users</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-shield-alt"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['otp_enabled_users']); ?></div>
                        <div class="text-muted">OTP Enabled</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                            <i class="fas fa-lock"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['require_2fa_users']); ?></div>
                        <div class="text-muted">2FA Required</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-danger text-white avatar">
                            <i class="fas fa-ban"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium"><?php echo number_format($stats['locked_users']); ?></div>
                        <div class="text-muted">Locked Users</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Bulk Actions -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter me-2"></i>
                    User Security Filters & Bulk Actions
                </h3>
                <div class="card-actions">
                    <a href="user-security-management.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-2"></i>
                        Clear Filters
                    </a>
                    <a href="security-settings.php" class="btn btn-info btn-sm">
                        <i class="fas fa-cog me-2"></i>
                        Global Settings
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-2 mb-3">
                    <div class="col-lg-2 col-md-3 col-6">
                        <label class="form-label">OTP Status</label>
                        <select name="otp" class="form-select form-select-sm">
                            <option value="">All OTP</option>
                            <option value="enabled" <?php echo $filter_otp === 'enabled' ? 'selected' : ''; ?>>Enabled</option>
                            <option value="disabled" <?php echo $filter_otp === 'disabled' ? 'selected' : ''; ?>>Disabled</option>
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-3 col-6">
                        <label class="form-label">2FA Requirement</label>
                        <select name="2fa" class="form-select form-select-sm">
                            <option value="">All 2FA</option>
                            <option value="required" <?php echo $filter_2fa === 'required' ? 'selected' : ''; ?>>Required</option>
                            <option value="optional" <?php echo $filter_2fa === 'optional' ? 'selected' : ''; ?>>Optional</option>
                        </select>
                    </div>

                    <div class="col-lg-2 col-md-3 col-6">
                        <label class="form-label">Lock Status</label>
                        <select name="locked" class="form-select form-select-sm">
                            <option value="">All Status</option>
                            <option value="locked" <?php echo $filter_locked === 'locked' ? 'selected' : ''; ?>>Locked</option>
                            <option value="unlocked" <?php echo $filter_locked === 'unlocked' ? 'selected' : ''; ?>>Unlocked</option>
                        </select>
                    </div>

                    <div class="col-lg-3 col-md-3 col-6">
                        <label class="form-label">Search User</label>
                        <input type="text" name="user" class="form-control form-control-sm" placeholder="Name, username, email..." value="<?php echo htmlspecialchars($filter_user); ?>">
                    </div>

                    <div class="col-lg-3 col-md-12 col-12">
                        <label class="form-label d-none d-lg-block">&nbsp;</label>
                        <div class="btn-group d-block" role="group">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>
                                Filter
                            </button>
                            <a href="user-security-management.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>
                                Clear
                            </a>
                        </div>
                    </div>
                </form>

                <!-- Bulk Actions -->
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <h4 class="alert-title">Bulk Actions</h4>
                            <form method="POST" action="" id="bulkForm">
                                <input type="hidden" name="action" value="bulk_update">
                                <div class="row g-2">
                                    <div class="col-lg-3 col-md-4 col-12">
                                        <select name="bulk_action" class="form-select form-select-sm" required>
                                            <option value="">Select Action...</option>
                                            <option value="enable_otp">Enable OTP for Selected</option>
                                            <option value="disable_otp">Disable OTP for Selected</option>
                                            <option value="require_2fa">Require 2FA for Selected</option>
                                            <option value="optional_2fa">Make 2FA Optional for Selected</option>
                                            <option value="unlock_accounts">Unlock Selected Accounts</option>
                                        </select>
                                    </div>
                                    <div class="col-lg-4 col-md-4 col-12">
                                        <input type="text" name="bulk_reason" class="form-control form-control-sm" placeholder="Reason for bulk change..." required>
                                    </div>
                                    <div class="col-lg-3 col-md-4 col-8">
                                        <button type="submit" class="btn btn-warning btn-sm" onclick="return confirmBulkAction()">
                                            <i class="fas fa-users me-1"></i>
                                            Apply to Selected
                                        </button>
                                    </div>
                                    <div class="col-lg-2 col-md-12 col-4">
                                        <span class="text-muted" id="selectedCount">0 selected</span>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-users me-2"></i>
                    User Security Settings
                    <span class="badge bg-secondary ms-2"><?php echo number_format($total_records); ?> users</span>
                </h3>
                <div class="card-actions">
                    <span class="text-muted">
                        Showing <?php echo number_format(($current_page - 1) * $records_per_page + 1); ?> -
                        <?php echo number_format(min($current_page * $records_per_page, $total_records)); ?>
                        of <?php echo number_format($total_records); ?> users
                    </span>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($users)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table table-sm">
                        <thead>
                            <tr>
                                <th class="w-1">
                                    <input type="checkbox" id="selectAll" class="form-check-input">
                                </th>
                                <th class="w-1">#</th>
                                <th>User</th>
                                <th>OTP</th>
                                <th>2FA</th>
                                <th>Lock Status</th>
                                <th>Settings</th>
                                <th>Last Activity</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $row_number = ($current_page - 1) * $records_per_page + 1;
                            foreach ($users as $user):
                                $is_locked = !empty($user['locked_until']) && strtotime($user['locked_until']) > time();
                            ?>
                            <tr>
                                <!-- Checkbox -->
                                <td>
                                    <input type="checkbox" name="user_ids[]" value="<?php echo $user['id']; ?>" class="form-check-input user-checkbox">
                                </td>

                                <!-- Row Number -->
                                <td>
                                    <span class="text-muted"><?php echo $row_number++; ?></span>
                                </td>

                                <!-- User Info -->
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-sm me-2">
                                            <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold" style="font-size: 0.85rem;">
                                                <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                            </div>
                                            <small class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></small>
                                            <br><small class="text-info"><?php echo htmlspecialchars($user['email']); ?></small>
                                        </div>
                                    </div>
                                </td>

                                <!-- OTP Status -->
                                <td>
                                    <span class="badge bg-<?php echo $user['otp_enabled'] ? 'success' : 'danger'; ?> badge-sm">
                                        <?php echo $user['otp_enabled'] ? 'Enabled' : 'Disabled'; ?>
                                    </span>
                                </td>

                                <!-- 2FA Status -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <span class="badge bg-<?php echo $user['require_2fa'] ? 'warning' : 'secondary'; ?> badge-sm mb-1">
                                            <?php echo $user['require_2fa'] ? 'Required' : 'Optional'; ?>
                                        </span>
                                        <?php if ($user['google_2fa_enabled']): ?>
                                        <span class="badge bg-info badge-sm">Google 2FA</span>
                                        <?php endif; ?>
                                    </div>
                                </td>

                                <!-- Lock Status -->
                                <td>
                                    <?php if ($is_locked): ?>
                                    <div class="d-flex flex-column">
                                        <span class="badge bg-danger badge-sm">Locked</span>
                                        <small class="text-muted">Until: <?php echo date('M j, g:i A', strtotime($user['locked_until'])); ?></small>
                                        <small class="text-danger">Attempts: <?php echo $user['failed_attempts']; ?></small>
                                    </div>
                                    <?php else: ?>
                                    <span class="badge bg-success badge-sm">Unlocked</span>
                                    <?php if ($user['failed_attempts'] > 0): ?>
                                    <br><small class="text-warning">Failed: <?php echo $user['failed_attempts']; ?></small>
                                    <?php endif; ?>
                                    <?php endif; ?>
                                </td>

                                <!-- Settings Summary -->
                                <td>
                                    <div class="d-flex flex-column" style="font-size: 0.8rem;">
                                        <span class="text-muted">Attempts: <?php echo $user['login_attempts_limit']; ?></span>
                                        <span class="text-muted">Lockout: <?php echo $user['lockout_duration']; ?>m</span>
                                        <span class="text-muted">OTP: <?php echo $user['otp_expiry_minutes']; ?>m</span>
                                        <?php if ($user['allow_remember_device']): ?>
                                        <span class="text-info">Remember Device</span>
                                        <?php endif; ?>
                                    </div>
                                </td>

                                <!-- Last Activity -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <?php if (!empty($user['last_login'])): ?>
                                        <div class="text-muted" style="font-size: 0.8rem;">
                                            Login: <?php echo date('M j, Y', strtotime($user['last_login'])); ?>
                                        </div>
                                        <?php endif; ?>
                                        <?php if (!empty($user['last_login_attempt'])): ?>
                                        <div class="text-muted" style="font-size: 0.8rem;">
                                            Attempt: <?php echo date('M j, g:i A', strtotime($user['last_login_attempt'])); ?>
                                        </div>
                                        <?php endif; ?>
                                        <div class="text-muted" style="font-size: 0.8rem;">
                                            Updated: <?php echo date('M j, Y', strtotime($user['updated_at'])); ?>
                                        </div>
                                    </div>
                                </td>

                                <!-- Actions -->
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="editUserSecurity('<?php echo $user['id']; ?>')" title="Edit Security Settings">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <?php if ($is_locked): ?>
                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="unlockUser('<?php echo $user['id']; ?>', '<?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>')" title="Unlock User">
                                            <i class="fas fa-unlock"></i>
                                        </button>
                                        <?php endif; ?>
                                        <button type="button" class="btn btn-sm btn-outline-info" onclick="viewSecurityHistory('<?php echo $user['id']; ?>')" title="View Security History">
                                            <i class="fas fa-history"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <div class="card-footer d-flex align-items-center">
                    <p class="m-0 text-muted">
                        Showing <span><?php echo number_format(($current_page - 1) * $records_per_page + 1); ?></span>
                        to <span><?php echo number_format(min($current_page * $records_per_page, $total_records)); ?></span>
                        of <span><?php echo number_format($total_records); ?></span> entries
                    </p>
                    <ul class="pagination m-0 ms-auto">
                        <?php if ($current_page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $current_page - 1; ?><?php echo !empty($_GET) ? '&' . http_build_query(array_diff_key($_GET, ['page' => ''])) : ''; ?>">
                                <i class="fas fa-chevron-left"></i> prev
                            </a>
                        </li>
                        <?php endif; ?>

                        <?php
                        $start_page = max(1, $current_page - 2);
                        $end_page = min($total_pages, $current_page + 2);

                        for ($i = $start_page; $i <= $end_page; $i++):
                        ?>
                        <li class="page-item <?php echo $i == $current_page ? 'active' : ''; ?>">
                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($_GET) ? '&' . http_build_query(array_diff_key($_GET, ['page' => ''])) : ''; ?>">
                                <?php echo $i; ?>
                            </a>
                        </li>
                        <?php endfor; ?>

                        <?php if ($current_page < $total_pages): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $current_page + 1; ?><?php echo !empty($_GET) ? '&' . http_build_query(array_diff_key($_GET, ['page' => ''])) : ''; ?>">
                                next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
                <?php endif; ?>
                <?php else: ?>
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-users" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No users found</p>
                    <p class="empty-subtitle text-muted">
                        <?php if (!empty(array_filter([$filter_otp, $filter_2fa, $filter_locked, $filter_user]))): ?>
                        Try adjusting your filters to see more results.
                        <?php else: ?>
                        No users with security settings found.
                        <?php endif; ?>
                    </p>
                    <div class="empty-action">
                        <a href="user-management.php" class="btn btn-primary">
                            <i class="fas fa-users me-2"></i>
                            Manage Users
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Security Modal -->
<div class="modal modal-blur fade" id="editUserSecurityModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-shield me-2"></i>
                    Edit Security Settings for <span id="edit_user_name"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="">
                <input type="hidden" name="action" value="update_user_security">
                <input type="hidden" name="user_id" id="edit_user_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Authentication Methods</h4>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-check">
                                            <input type="checkbox" name="otp_enabled" id="edit_otp_enabled" class="form-check-input">
                                            <span class="form-check-label">
                                                <strong>Enable OTP Authentication</strong>
                                                <span class="form-check-description">Send OTP codes via email for login verification</span>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-check">
                                            <input type="checkbox" name="google_2fa_enabled" id="edit_google_2fa_enabled" class="form-check-input">
                                            <span class="form-check-label">
                                                <strong>Enable Google 2FA</strong>
                                                <span class="form-check-description">Use Google Authenticator for 2FA</span>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-check">
                                            <input type="checkbox" name="require_2fa" id="edit_require_2fa" class="form-check-input">
                                            <span class="form-check-label">
                                                <strong>Require 2FA</strong>
                                                <span class="form-check-description">Make 2FA mandatory for this user</span>
                                            </span>
                                        </label>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-check">
                                            <input type="checkbox" name="allow_remember_device" id="edit_allow_remember_device" class="form-check-input">
                                            <span class="form-check-label">
                                                <strong>Allow Remember Device</strong>
                                                <span class="form-check-description">User can skip 2FA on trusted devices</span>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">Security Settings</h4>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">Login Attempts Limit</label>
                                        <input type="number" name="login_attempts_limit" id="edit_login_attempts_limit" class="form-control form-control-lg" min="1" max="20" value="5">
                                        <small class="form-hint">Maximum failed login attempts before lockout</small>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Lockout Duration (minutes)</label>
                                        <input type="number" name="lockout_duration" id="edit_lockout_duration" class="form-control form-control-lg" min="1" max="1440" value="30">
                                        <small class="form-hint">How long account remains locked after failed attempts</small>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">OTP Expiry (minutes)</label>
                                        <input type="number" name="otp_expiry_minutes" id="edit_otp_expiry_minutes" class="form-control form-control-lg" min="1" max="60" value="10">
                                        <small class="form-hint">How long OTP codes remain valid</small>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Reason for Change</label>
                                        <input type="text" name="reason" class="form-control form-control-lg" placeholder="Reason for security settings change..." required>
                                        <small class="form-hint">Required for audit trail</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        Save Security Settings
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Security History Modal -->
<div class="modal modal-blur fade" id="securityHistoryModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-history me-2"></i>
                    Security History for <span id="history_user_name"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-vcenter table-sm">
                        <thead>
                            <tr>
                                <th class="w-1">#</th>
                                <th>Setting Changed</th>
                                <th>Old Value</th>
                                <th>New Value</th>
                                <th>Reason</th>
                                <th>Changed By</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody id="security_history_table_body">
                            <!-- History records will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Select All functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.user-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateSelectedCount();
});

// Update selected count
function updateSelectedCount() {
    const selected = document.querySelectorAll('.user-checkbox:checked').length;
    document.getElementById('selectedCount').textContent = selected + ' selected';

    // Add selected user IDs to bulk form
    const bulkForm = document.getElementById('bulkForm');
    const existingInputs = bulkForm.querySelectorAll('input[name="user_ids[]"]');
    existingInputs.forEach(input => input.remove());

    document.querySelectorAll('.user-checkbox:checked').forEach(checkbox => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'user_ids[]';
        input.value = checkbox.value;
        bulkForm.appendChild(input);
    });
}

// Add event listeners to individual checkboxes
document.querySelectorAll('.user-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', updateSelectedCount);
});

// Confirm bulk action
function confirmBulkAction() {
    const selected = document.querySelectorAll('.user-checkbox:checked').length;
    if (selected === 0) {
        alert('Please select at least one user.');
        return false;
    }

    const action = document.querySelector('select[name="bulk_action"]').value;
    const reason = document.querySelector('input[name="bulk_reason"]').value;

    if (!action || !reason) {
        alert('Please select an action and provide a reason.');
        return false;
    }

    return confirm(`Are you sure you want to apply "${action}" to ${selected} selected users?\n\nReason: ${reason}`);
}

// Edit user security settings
function editUserSecurity(userId) {
    // Fetch user security settings via AJAX
    fetch('ajax/get_user_security.php?user_id=' + userId)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Populate the edit form with current settings
                document.getElementById('edit_user_id').value = userId;
                document.getElementById('edit_user_name').textContent = data.user.first_name + ' ' + data.user.last_name;
                document.getElementById('edit_otp_enabled').checked = data.settings.otp_enabled == 1;
                document.getElementById('edit_google_2fa_enabled').checked = data.settings.google_2fa_enabled == 1;
                document.getElementById('edit_require_2fa').checked = data.settings.require_2fa == 1;
                document.getElementById('edit_allow_remember_device').checked = data.settings.allow_remember_device == 1;
                document.getElementById('edit_login_attempts_limit').value = data.settings.login_attempts_limit;
                document.getElementById('edit_lockout_duration').value = data.settings.lockout_duration;
                document.getElementById('edit_otp_expiry_minutes').value = data.settings.otp_expiry_minutes;

                // Show the modal
                $('#editUserSecurityModal').modal('show');
            } else {
                alert('Error loading user security settings: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to load user security settings. Please try again.');
        });
}

// Unlock user
function unlockUser(userId, userName) {
    const reason = prompt(`Unlock account for ${userName}?\n\nPlease provide a reason:`, 'Admin unlock - account review completed');
    if (reason !== null && reason.trim() !== '') {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="unlock_user">
            <input type="hidden" name="user_id" value="${userId}">
            <input type="hidden" name="reason" value="${reason}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// View security history
function viewSecurityHistory(userId) {
    // Fetch user security history via AJAX
    fetch('ajax/get_security_history.php?user_id=' + userId)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Populate the history modal
                document.getElementById('history_user_name').textContent = data.user.first_name + ' ' + data.user.last_name;

                const historyTableBody = document.getElementById('security_history_table_body');
                historyTableBody.innerHTML = '';

                if (data.history.length > 0) {
                    data.history.forEach((record, index) => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <td>${index + 1}</td>
                            <td><span class="badge bg-info badge-sm">${record.setting_changed}</span></td>
                            <td><span class="text-muted">${record.old_value || 'N/A'}</span></td>
                            <td><span class="text-success">${record.new_value}</span></td>
                            <td><small class="text-muted">${record.reason || 'No reason provided'}</small></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-xs me-2">${record.changed_by_first_name ? record.changed_by_first_name.charAt(0) + record.changed_by_last_name.charAt(0) : 'SY'}</div>
                                    <div style="font-size: 0.8rem;">${record.changed_by_first_name ? record.changed_by_first_name + ' ' + record.changed_by_last_name : 'System'}</div>
                                </div>
                            </td>
                            <td><small class="text-muted">${new Date(record.changed_at).toLocaleString()}</small></td>
                        `;
                        historyTableBody.appendChild(row);
                    });
                } else {
                    historyTableBody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No security changes found for this user.</td></tr>';
                }

                // Show the modal
                $('#securityHistoryModal').modal('show');
            } else {
                alert('Error loading security history: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to load security history. Please try again.');
        });
}
</script>

<?php include 'includes/admin-footer.php'; ?>

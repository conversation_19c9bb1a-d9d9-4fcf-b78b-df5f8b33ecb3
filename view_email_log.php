<?php
require_once 'config/config.php';

$log_file = __DIR__ . '/logs/email_simulation.log';
$log_content = '';

if (file_exists($log_file)) {
    $log_content = file_get_contents($log_file);
} else {
    $log_content = "No email log found. Emails will be logged here when the OTP system is used.";
}

// Clear log functionality
if (isset($_GET['clear'])) {
    file_put_contents($log_file, '');
    $log_content = "Email log cleared.";
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Email Simulation Log</title>
    <style>
        body { font-family: 'Courier New', monospace; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #007cba; color: white; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 8px 8px 0 0; }
        .log-content { background: #1e1e1e; color: #00ff00; padding: 20px; border-radius: 5px; white-space: pre-wrap; font-size: 14px; max-height: 600px; overflow-y: auto; }
        .button { background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }
        .button:hover { background: #c82333; }
        .button.refresh { background: #28a745; }
        .button.refresh:hover { background: #1e7e34; }
        .info { background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0; border-left: 4px solid #007cba; }
        .empty { text-align: center; color: #666; font-style: italic; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 style="margin: 0;">📧 Email Simulation Log</h1>
            <p style="margin: 5px 0 0 0;">View OTP emails that would be sent during localhost testing</p>
        </div>
        
        <div class="info">
            <strong>How it works:</strong> Since we're on localhost, actual emails can't be sent reliably. 
            Instead, the system logs what would be sent here. In production, real emails would be delivered.
        </div>
        
        <div style="margin: 20px 0;">
            <a href="?" class="button refresh">🔄 Refresh Log</a>
            <a href="?clear=1" class="button" onclick="return confirm('Clear the entire email log?')">🗑️ Clear Log</a>
            <a href="auth/login.php" class="button refresh">🔐 Test Login</a>
        </div>
        
        <h3>Email Log Contents:</h3>
        <div class="log-content">
            <?php if (empty(trim($log_content)) || $log_content === "Email log cleared."): ?>
                <div class="empty">No emails logged yet. Try logging in to generate OTP emails.</div>
            <?php else: ?>
                <?php echo htmlspecialchars($log_content); ?>
            <?php endif; ?>
        </div>
        
        <div class="info">
            <strong>Testing Tips:</strong>
            <ul>
                <li>Create test users with <a href="create_test_user.php">create_test_user.php</a></li>
                <li>Try logging in with fake email to see error handling</li>
                <li>Try logging in with real email to test actual delivery</li>
                <li>Check admin panel to see OTP codes for user support</li>
            </ul>
        </div>
    </div>
    
    <script>
        // Auto-refresh every 30 seconds
        setTimeout(function() {
            window.location.reload();
        }, 30000);
    </script>
</body>
</html>

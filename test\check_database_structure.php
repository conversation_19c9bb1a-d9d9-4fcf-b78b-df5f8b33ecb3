<?php
require_once '../config/config.php';

echo "<h2>Database Structure Analysis</h2>";

try {
    $db = getDB();
    
    // Check accounts table structure
    echo "<h3>Accounts Table Structure:</h3>";
    $result = $db->query("DESCRIBE accounts");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['Field']}</td>";
        echo "<td>{$row['Type']}</td>";
        echo "<td>{$row['Null']}</td>";
        echo "<td>{$row['Key']}</td>";
        echo "<td>{$row['Default']}</td>";
        echo "<td>{$row['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check current users
    echo "<h3>Current Users in Database:</h3>";
    $result = $db->query("SELECT id, username, email, first_name, last_name, is_admin FROM accounts ORDER BY id");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>Is Admin</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['username']}</td>";
        echo "<td>{$row['email']}</td>";
        echo "<td>{$row['first_name']} {$row['last_name']}</td>";
        echo "<td>" . ($row['is_admin'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check for related tables
    echo "<h3>Related Tables Check:</h3>";
    $tables_to_check = [
        'user_otps',
        'transactions', 
        'virtual_cards',
        'user_sessions'
    ];
    
    foreach ($tables_to_check as $table) {
        echo "<h4>Table: $table</h4>";
        try {
            $result = $db->query("SHOW TABLES LIKE '$table'");
            if ($result->num_rows > 0) {
                echo "✅ Table exists<br>";
                
                // Show structure
                $result = $db->query("DESCRIBE $table");
                echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
                echo "<tr><th>Field</th><th>Type</th><th>Key</th></tr>";
                while ($row = $result->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>{$row['Field']}</td>";
                    echo "<td>{$row['Type']}</td>";
                    echo "<td>{$row['Key']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                // Count records
                $result = $db->query("SELECT COUNT(*) as count FROM $table");
                $count = $result->fetch_assoc()['count'];
                echo "Records: $count<br><br>";
            } else {
                echo "❌ Table does not exist<br><br>";
            }
        } catch (Exception $e) {
            echo "❌ Error checking table: " . $e->getMessage() . "<br><br>";
        }
    }
    
    // Test delete query
    echo "<h3>Test Delete Query (DRY RUN):</h3>";
    $test_user_id = 6; // Demo Home user ID
    
    echo "<p><strong>User to delete (ID: $test_user_id):</strong></p>";
    $result = $db->query("SELECT * FROM accounts WHERE id = ?", [$test_user_id]);
    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        echo "<pre>";
        print_r($user);
        echo "</pre>";
        
        echo "<p><strong>Related records that would be deleted:</strong></p>";
        
        foreach ($tables_to_check as $table) {
            try {
                $result = $db->query("SHOW TABLES LIKE '$table'");
                if ($result->num_rows > 0) {
                    // Check for user_id or account_id column
                    $result = $db->query("DESCRIBE $table");
                    $has_user_id = false;
                    $has_account_id = false;
                    
                    while ($row = $result->fetch_assoc()) {
                        if ($row['Field'] === 'user_id') $has_user_id = true;
                        if ($row['Field'] === 'account_id') $has_account_id = true;
                    }
                    
                    if ($has_user_id) {
                        $result = $db->query("SELECT COUNT(*) as count FROM $table WHERE user_id = ?", [$test_user_id]);
                        $count = $result->fetch_assoc()['count'];
                        echo "- $table (user_id): $count records<br>";
                    }
                    
                    if ($has_account_id) {
                        $result = $db->query("SELECT COUNT(*) as count FROM $table WHERE account_id = ?", [$test_user_id]);
                        $count = $result->fetch_assoc()['count'];
                        echo "- $table (account_id): $count records<br>";
                    }
                }
            } catch (Exception $e) {
                echo "- $table: Error - " . $e->getMessage() . "<br>";
            }
        }
    } else {
        echo "❌ User with ID $test_user_id not found!";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
h2, h3, h4 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
</style>

<?php
require_once '../config/config.php';

echo "<h2>🧪 Simple Delete Test</h2>";

try {
    $db = getDB();
    $user_id = 6; // <EMAIL>
    
    echo "<h3>Step 1: Check if user exists</h3>";
    $check_result = $db->query("SELECT id, username, email FROM accounts WHERE id = ?", [$user_id]);
    
    if ($check_result->num_rows > 0) {
        $user = $check_result->fetch_assoc();
        echo "✅ User found: {$user['username']} ({$user['email']})<br>";
        
        echo "<h3>Step 2: Attempt to delete user</h3>";
        
        // Use the fixed delete method
        $affected_rows = $db->delete("DELETE FROM accounts WHERE id = ? AND is_admin = 0", [$user_id]);
        
        echo "Delete operation result: $affected_rows affected rows<br>";
        
        if ($affected_rows > 0) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h4>🎉 Success!</h4>";
            echo "<p>User has been successfully deleted!</p>";
            echo "<p>Affected rows: $affected_rows</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h4>❌ Failed!</h4>";
            echo "<p>No rows were affected. User may not exist or may be an admin.</p>";
            echo "<p>Affected rows: $affected_rows</p>";
            echo "</div>";
        }
        
        echo "<h3>Step 3: Verify deletion</h3>";
        $verify_result = $db->query("SELECT id, username, email FROM accounts WHERE id = ?", [$user_id]);
        
        if ($verify_result->num_rows === 0) {
            echo "✅ Confirmed: User has been deleted from database<br>";
        } else {
            echo "❌ User still exists in database<br>";
        }
        
    } else {
        echo "❌ User with ID $user_id not found<br>";
    }
    
    echo "<h3>Step 4: Current users in database</h3>";
    $all_users = $db->query("SELECT id, username, email, is_admin FROM accounts ORDER BY id");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f2f2f2;'><th>ID</th><th>Username</th><th>Email</th><th>Is Admin</th></tr>";
    
    while ($row = $all_users->fetch_assoc()) {
        $highlight = ($row['id'] == $user_id) ? "style='background: #ffcccc;'" : "";
        echo "<tr $highlight>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['username']}</td>";
        echo "<td>{$row['email']}</td>";
        echo "<td>" . ($row['is_admin'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>❌ Error!</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
h2, h3 { color: #333; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>

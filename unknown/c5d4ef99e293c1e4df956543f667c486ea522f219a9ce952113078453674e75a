<?php
require_once '../config/config.php';

echo "<h1>🌐 Browser Delete Test Setup</h1>";

// This file will create a test user and provide instructions for browser testing
try {
    $db = getDB();
    
    // Create a test user for browser testing
    $test_user_data = [
        'username' => 'browsertest_' . time(),
        'email' => 'browserdelete_' . time() . '@example.com',
        'password' => 'TestPassword123!',
        'first_name' => 'Browser',
        'last_name' => 'TestUser',
        'phone' => '+**********',
        'address' => '123 Browser Test Street',
        'currency' => 'USD',
        'account_type' => 'savings',
        'initial_balance' => 250.00
    ];
    
    // Generate account number
    function generateTestAccountNumber() {
        return 'ACC' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
    }
    
    $account_number = generateTestAccountNumber();
    $hashed_password = password_hash($test_user_data['password'], PASSWORD_DEFAULT);
    
    $sql = "INSERT INTO accounts (
                account_number, username, password, email, first_name, last_name,
                phone, address, currency, account_type, balance, status, kyc_status, is_admin
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', 'verified', 0)";
    
    $params = [
        $account_number,
        $test_user_data['username'],
        $hashed_password,
        $test_user_data['email'],
        $test_user_data['first_name'],
        $test_user_data['last_name'],
        $test_user_data['phone'],
        $test_user_data['address'],
        $test_user_data['currency'],
        $test_user_data['account_type'],
        $test_user_data['initial_balance']
    ];
    
    $created_user_id = $db->insert($sql, $params);
    
    if ($created_user_id) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h2>✅ Test User Created for Browser Testing!</h2>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; background: white;'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        echo "<tr><td><strong>User ID</strong></td><td>$created_user_id</td></tr>";
        echo "<tr><td><strong>Username</strong></td><td>{$test_user_data['username']}</td></tr>";
        echo "<tr><td><strong>Email</strong></td><td>{$test_user_data['email']}</td></tr>";
        echo "<tr><td><strong>Account Number</strong></td><td>$account_number</td></tr>";
        echo "<tr><td><strong>Balance</strong></td><td>\${$test_user_data['initial_balance']}</td></tr>";
        echo "</table>";
        echo "</div>";
        
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h2>🧪 Browser Testing Instructions</h2>";
        echo "<ol>";
        echo "<li><strong>Login to Admin:</strong> Go to <a href='http://localhost/online_banking/admin/login.php' target='_blank'>http://localhost/online_banking/admin/login.php</a></li>";
        echo "<li><strong>Navigate to Users:</strong> Go to <a href='http://localhost/online_banking/admin/users.php' target='_blank'>http://localhost/online_banking/admin/users.php</a></li>";
        echo "<li><strong>Find Test User:</strong> Look for user with ID <strong>$created_user_id</strong> and username <strong>{$test_user_data['username']}</strong></li>";
        echo "<li><strong>Test Delete:</strong> Click the red trash icon for this user</li>";
        echo "<li><strong>Verify Modal:</strong> Confirm the delete modal shows correct user information</li>";
        echo "<li><strong>Confirm Delete:</strong> Click 'Delete User' button</li>";
        echo "<li><strong>Verify Removal:</strong> Check that the user row disappears from the table</li>";
        echo "<li><strong>Verify Database:</strong> Refresh the page to confirm user is permanently deleted</li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h3>⚠️ What to Test</h3>";
        echo "<ul>";
        echo "<li><strong>Modal Display:</strong> Delete confirmation modal should show user details</li>";
        echo "<li><strong>AJAX Delete:</strong> User row should fade out and disappear</li>";
        echo "<li><strong>User Count:</strong> Total user count should decrease by 1</li>";
        echo "<li><strong>Database Persistence:</strong> User should be permanently removed</li>";
        echo "<li><strong>Error Handling:</strong> Try deleting the same user twice (should fail gracefully)</li>";
        echo "</ul>";
        echo "</div>";
        
        // Show current users for reference
        echo "<h2>📋 Current Users in Database</h2>";
        $all_users = $db->query("SELECT id, username, email, first_name, last_name, balance, status, is_admin FROM accounts ORDER BY id DESC LIMIT 10");
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0; background: white;'>";
        echo "<tr style='background: #f2f2f2;'>";
        echo "<th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>Balance</th><th>Status</th><th>Admin</th>";
        echo "</tr>";
        
        while ($row = $all_users->fetch_assoc()) {
            $highlight = ($row['id'] == $created_user_id) ? "style='background: #ffffcc; font-weight: bold;'" : "";
            echo "<tr $highlight>";
            echo "<td>{$row['id']}</td>";
            echo "<td>{$row['username']}</td>";
            echo "<td>{$row['email']}</td>";
            echo "<td>{$row['first_name']} {$row['last_name']}</td>";
            echo "<td>\${$row['balance']}</td>";
            echo "<td>{$row['status']}</td>";
            echo "<td>" . ($row['is_admin'] ? 'Yes' : 'No') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h3>🧹 Cleanup</h3>";
        echo "<p>After testing, you can clean up by running: <a href='cleanup_test_users.php' target='_blank'>cleanup_test_users.php</a></p>";
        echo "<p>Or manually delete test users that start with 'browsertest_' or 'testuser_'</p>";
        echo "</div>";
        
    } else {
        throw new Exception("Failed to create test user for browser testing");
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>❌ Error Creating Test User!</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f8f9fa; 
    line-height: 1.6;
}
h1, h2, h3 { color: #333; }
h1 { border-bottom: 3px solid #007bff; padding-bottom: 10px; }
h2 { border-bottom: 1px solid #dee2e6; padding-bottom: 5px; }
table { 
    border-collapse: collapse; 
    width: 100%; 
    margin: 10px 0; 
    background: white;
}
th, td { 
    border: 1px solid #ddd; 
    padding: 8px; 
    text-align: left; 
}
th { 
    background-color: #f2f2f2; 
    font-weight: bold;
}
ol, ul { 
    padding-left: 20px; 
}
p { 
    margin: 5px 0; 
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
</style>

<?php
require_once 'config/config.php';

// Simulate admin session
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['is_admin'] = true;

echo "<h2>Delete User Functionality Test</h2>";
echo "<hr>";

// Find a non-admin user to test with
try {
    $db = getDB();
    $test_user_query = "SELECT id, username, email, first_name, last_name FROM accounts WHERE is_admin = 0 LIMIT 1";
    $test_user_result = $db->query($test_user_query);
    
    if ($test_user_result->num_rows === 0) {
        echo "❌ No non-admin users found to test with<br>";
        echo "Creating a test user first...<br>";
        
        // Create a test user
        $test_account_number = generateAccountNumber();
        $test_password = hashPassword('testpass123');
        
        $insert_sql = "INSERT INTO accounts (account_number, username, password, email, first_name, last_name, account_type, balance, status, kyc_status) 
                       VALUES (?, 'test_delete_user', ?, '<EMAIL>', 'Test', 'Delete', 'savings', 100.00, 'active', 'verified')";
        
        $test_user_id = $db->insert($insert_sql, [$test_account_number, $test_password]);
        
        if ($test_user_id) {
            echo "✅ Test user created with ID: $test_user_id<br>";
            $test_user = [
                'id' => $test_user_id,
                'username' => 'test_delete_user',
                'email' => '<EMAIL>',
                'first_name' => 'Test',
                'last_name' => 'Delete'
            ];
        } else {
            echo "❌ Failed to create test user<br>";
            exit;
        }
    } else {
        $test_user = $test_user_result->fetch_assoc();
        echo "✅ Using existing user for test: " . $test_user['username'] . " (ID: " . $test_user['id'] . ")<br>";
    }
    
    echo "<br><h3>Testing Delete Process:</h3>";
    
    $user_id = $test_user['id'];
    
    // Test the delete logic step by step
    echo "🧪 Starting transaction...<br>";
    $db->query("START TRANSACTION");
    
    try {
        echo "🗑️ Deleting OTP records...<br>";
        try {
            $otp_affected = $db->query("DELETE FROM user_otps WHERE user_id = ?", [$user_id]);
            echo "&nbsp;&nbsp;&nbsp;✅ OTP records processed<br>";
        } catch (Exception $e) {
            echo "&nbsp;&nbsp;&nbsp;⚠️ OTP table issue (likely doesn't exist): " . $e->getMessage() . "<br>";
        }

        echo "🗑️ Deleting account transactions...<br>";
        try {
            $db->query("DELETE FROM account_transactions WHERE account_id = ?", [$user_id]);
            echo "&nbsp;&nbsp;&nbsp;✅ Account transactions processed<br>";
        } catch (Exception $e) {
            echo "&nbsp;&nbsp;&nbsp;⚠️ Account transactions table issue: " . $e->getMessage() . "<br>";
        }

        echo "🗑️ Deleting transfers (sender)...<br>";
        try {
            $db->query("DELETE FROM transfers WHERE sender_id = ?", [$user_id]);
            echo "&nbsp;&nbsp;&nbsp;✅ Sender transfers processed<br>";
        } catch (Exception $e) {
            echo "&nbsp;&nbsp;&nbsp;⚠️ Transfers table issue: " . $e->getMessage() . "<br>";
        }

        echo "🗑️ Deleting transfers (recipient)...<br>";
        try {
            $db->query("DELETE FROM transfers WHERE recipient_id = ?", [$user_id]);
            echo "&nbsp;&nbsp;&nbsp;✅ Recipient transfers processed<br>";
        } catch (Exception $e) {
            echo "&nbsp;&nbsp;&nbsp;⚠️ Transfers table issue: " . $e->getMessage() . "<br>";
        }

        echo "🗑️ Deleting virtual cards...<br>";
        try {
            $db->query("DELETE FROM virtual_cards WHERE user_id = ?", [$user_id]);
            echo "&nbsp;&nbsp;&nbsp;✅ Virtual cards processed<br>";
        } catch (Exception $e) {
            echo "&nbsp;&nbsp;&nbsp;⚠️ Virtual cards table issue: " . $e->getMessage() . "<br>";
        }

        echo "🗑️ Deleting beneficiaries...<br>";
        try {
            $db->query("DELETE FROM beneficiaries WHERE user_id = ?", [$user_id]);
            echo "&nbsp;&nbsp;&nbsp;✅ Beneficiaries processed<br>";
        } catch (Exception $e) {
            echo "&nbsp;&nbsp;&nbsp;⚠️ Beneficiaries table issue: " . $e->getMessage() . "<br>";
        }

        echo "🗑️ Deleting support tickets...<br>";
        try {
            $db->query("DELETE FROM tickets WHERE user_id = ?", [$user_id]);
            echo "&nbsp;&nbsp;&nbsp;✅ Support tickets processed<br>";
        } catch (Exception $e) {
            echo "&nbsp;&nbsp;&nbsp;⚠️ Support tickets table issue: " . $e->getMessage() . "<br>";
        }

        echo "🗑️ Deleting crypto accounts...<br>";
        try {
            $db->query("DELETE FROM crypto_accounts WHERE user_id = ?", [$user_id]);
            echo "&nbsp;&nbsp;&nbsp;✅ Crypto accounts processed<br>";
        } catch (Exception $e) {
            echo "&nbsp;&nbsp;&nbsp;⚠️ Crypto accounts table issue: " . $e->getMessage() . "<br>";
        }

        echo "🗑️ Finally, deleting user account...<br>";
        $affected_rows = $db->delete("DELETE FROM accounts WHERE id = ? AND is_admin = 0", [$user_id]);
        
        if ($affected_rows > 0) {
            echo "&nbsp;&nbsp;&nbsp;✅ User account deleted successfully! (Affected rows: $affected_rows)<br>";
            $db->query("COMMIT");
            echo "✅ Transaction committed<br>";
            
            echo "<br><h3>✅ SUCCESS: Delete functionality is working!</h3>";
            echo "User '{$test_user['username']}' has been permanently deleted.<br>";
        } else {
            echo "&nbsp;&nbsp;&nbsp;❌ Failed to delete user account (Affected rows: $affected_rows)<br>";
            $db->query("ROLLBACK");
            echo "❌ Transaction rolled back<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error during delete process: " . $e->getMessage() . "<br>";
        $db->query("ROLLBACK");
        echo "❌ Transaction rolled back<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

echo "<br><a href='admin/users.php'>← Back to User Management</a>";
?>

<?php
require_once 'config/config.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$page_title = 'Session Debug';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.3.2/dist/css/tabler.min.css" rel="stylesheet">
</head>
<body>
    <div class="page-wrapper">
        <div class="container-xl">
            <div class="page-header">
                <h1>Session Debug Information</h1>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Session Data</h3>
                        </div>
                        <div class="card-body">
                            <pre><?php print_r($_SESSION); ?></pre>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Function Results</h3>
                        </div>
                        <div class="card-body">
                            <p><strong>isLoggedIn():</strong> <?php echo isLoggedIn() ? 'true' : 'false'; ?></p>
                            <p><strong>isAdmin():</strong> <?php echo isAdmin() ? 'true' : 'false'; ?></p>
                            
                            <?php if (isLoggedIn()): ?>
                            <hr>
                            <p><strong>User ID:</strong> <?php echo $_SESSION['user_id'] ?? 'Not set'; ?></p>
                            <p><strong>Username:</strong> <?php echo $_SESSION['username'] ?? 'Not set'; ?></p>
                            <p><strong>First Name:</strong> <?php echo $_SESSION['first_name'] ?? 'Not set'; ?></p>
                            <p><strong>Last Name:</strong> <?php echo $_SESSION['last_name'] ?? 'Not set'; ?></p>
                            <p><strong>Is Admin (raw):</strong> <?php var_dump($_SESSION['is_admin'] ?? 'Not set'); ?></p>
                            <p><strong>Is Admin (bool):</strong> <?php echo isset($_SESSION['is_admin']) ? ($_SESSION['is_admin'] ? 'true' : 'false') : 'Not set'; ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Database Check</h3>
                        </div>
                        <div class="card-body">
                            <?php if (isLoggedIn()): ?>
                                <?php
                                try {
                                    $db = getDB();
                                    $user_id = $_SESSION['user_id'];
                                    $result = $db->query("SELECT id, username, first_name, last_name, is_admin, status FROM accounts WHERE id = ?", [$user_id]);
                                    
                                    if ($result && $result->num_rows > 0) {
                                        $user = $result->fetch_assoc();
                                        echo "<h5>Database User Data:</h5>";
                                        echo "<pre>";
                                        print_r($user);
                                        echo "</pre>";
                                    } else {
                                        echo "<p class='text-danger'>User not found in database!</p>";
                                    }
                                } catch (Exception $e) {
                                    echo "<p class='text-danger'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
                                }
                                ?>
                            <?php else: ?>
                                <p>Not logged in - cannot check database.</p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Actions</h3>
                        </div>
                        <div class="card-body">
                            <div class="btn-list">
                                <a href="auth/login.php" class="btn btn-primary">User Login</a>
                                <a href="admin/login.php" class="btn btn-danger">Admin Login</a>
                                <a href="dashboard/" class="btn btn-info">User Dashboard</a>
                                <a href="admin/" class="btn btn-warning">Admin Dashboard</a>
                                <a href="auth/logout.php" class="btn btn-secondary">Logout</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

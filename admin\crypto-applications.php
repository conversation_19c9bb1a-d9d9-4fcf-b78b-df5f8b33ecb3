<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Crypto Wallet Applications';

// Handle application review
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $db = getDB();
        
        $application_id = intval($_POST['application_id']);
        $action = $_POST['action'];
        $review_notes = trim($_POST['review_notes'] ?? '');
        
        if ($action === 'approve') {
            // Get application details
            $app_query = "SELECT * FROM crypto_wallet_applications WHERE application_id = ? AND status = 'pending'";
            $app_result = $db->query($app_query, [$application_id]);
            $application = $app_result->fetch_assoc();
            
            if (!$application) {
                throw new Exception("Application not found or already processed.");
            }
            
            // Check if user already has a wallet for this cryptocurrency
            $existing_check = "SELECT wallet_id FROM crypto_wallets WHERE account_id = ? AND cryptocurrency = ?";
            $existing_result = $db->query($existing_check, [$application['account_id'], $application['cryptocurrency']]);
            if ($existing_result->num_rows > 0) {
                throw new Exception("User already has a {$application['cryptocurrency']} wallet.");
            }
            
            // Generate wallet address
            function generateWalletAddress($crypto) {
                $prefixes = [
                    'BTC' => ['1', '3', 'bc1'],
                    'ETH' => ['0x'],
                    'LTC' => ['L', 'M', 'ltc1'],
                    'BCH' => ['1', '3', 'q'],
                    'ADA' => ['addr1'],
                    'DOT' => ['1']
                ];
                
                $prefix = $prefixes[$crypto][array_rand($prefixes[$crypto])];
                $length = ($crypto === 'ETH') ? 40 : (($crypto === 'ADA') ? 58 : 34);
                
                $chars = '**********************************************************';
                $address = $prefix;
                
                for ($i = strlen($prefix); $i < $length; $i++) {
                    $address .= $chars[rand(0, strlen($chars) - 1)];
                }
                
                return $address;
            }
            
            // Create crypto wallet
            $wallet_address = generateWalletAddress($application['cryptocurrency']);
            $wallet_name = "Wallet for " . $application['cryptocurrency'];
            $private_key = bin2hex(random_bytes(32));
            $public_key = bin2hex(random_bytes(33));
            
            $insert_wallet = "INSERT INTO crypto_wallets (
                account_id, wallet_address, wallet_name, cryptocurrency, wallet_type,
                private_key, public_key, daily_limit, monthly_limit, 
                status, approved_by, approved_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', ?, NOW())";
            
            $wallet_id = $db->insert($insert_wallet, [
                $application['account_id'], $wallet_address, $wallet_name, $application['cryptocurrency'], 
                $application['wallet_type'], $private_key, $public_key, $application['requested_limit'], 
                $application['requested_limit'] * 30, $_SESSION['user_id']
            ]);
            
            if ($wallet_id) {
                // Update application status
                $update_app = "UPDATE crypto_wallet_applications SET 
                              status = 'approved', reviewed_at = NOW(), reviewed_by = ?, 
                              review_notes = ?, wallet_id = ? WHERE application_id = ?";
                $db->query($update_app, [$_SESSION['user_id'], $review_notes, $wallet_id, $application_id]);
                
                $success = "✅ Application approved and crypto wallet created successfully! Wallet ID: {$wallet_id}";
            } else {
                throw new Exception("Failed to create crypto wallet.");
            }
            
        } elseif ($action === 'reject') {
            // Update application status to rejected
            $update_app = "UPDATE crypto_wallet_applications SET 
                          status = 'rejected', reviewed_at = NOW(), reviewed_by = ?, 
                          review_notes = ? WHERE application_id = ? AND status = 'pending'";
            $result = $db->query($update_app, [$_SESSION['user_id'], $review_notes, $application_id]);
            
            if ($result) {
                $success = "✅ Application rejected successfully.";
            } else {
                throw new Exception("Failed to reject application.");
            }
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Pagination settings
$records_per_page = 25;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

// Filter parameters
$filter_status = $_GET['status'] ?? '';
$filter_crypto = $_GET['crypto'] ?? '';

// Build WHERE clause for filters
$where_conditions = [];
$params = [];

if (!empty($filter_status)) {
    $where_conditions[] = "cwa.status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_crypto)) {
    $where_conditions[] = "cwa.cryptocurrency = ?";
    $params[] = $filter_crypto;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();
    
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total 
                    FROM crypto_wallet_applications cwa 
                    LEFT JOIN accounts a ON cwa.account_id = a.id 
                    $where_clause";
    $count_result = $db->query($count_query, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);
    
    // Get applications with pagination
    $applications_query = "SELECT cwa.*, 
                          a.first_name, a.last_name, a.username, a.account_number,
                          admin.first_name as admin_first_name, admin.last_name as admin_last_name
                          FROM crypto_wallet_applications cwa 
                          LEFT JOIN accounts a ON cwa.account_id = a.id 
                          LEFT JOIN accounts admin ON cwa.reviewed_by = admin.id 
                          $where_clause
                          ORDER BY cwa.applied_at DESC 
                          LIMIT $records_per_page OFFSET $offset";
    
    $applications_result = $db->query($applications_query, $params);
    $applications = [];
    while ($row = $applications_result->fetch_assoc()) {
        $applications[] = $row;
    }
    
} catch (Exception $e) {
    $error = "Failed to load crypto wallet applications: " . $e->getMessage();
    $applications = [];
    $total_records = 0;
    $total_pages = 0;
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="crypto-wallets.php">Crypto Wallets</a></li>
        <li class="breadcrumb-item active" aria-current="page">Applications</li>
    </ol>
</nav>

<!-- Success Message -->
<?php if (isset($success)): ?>
<div class="alert alert-success alert-dismissible" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-check-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Success!</h4>
            <div class="text-muted"><?php echo $success; ?></div>
        </div>
    </div>
    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
</div>
<?php endif; ?>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Filters Card -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter me-2"></i>
                    Application Filters
                </h3>
                <div class="card-actions">
                    <a href="crypto-applications.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-2"></i>
                        Clear Filters
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-2">
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select form-select-sm">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="approved" <?php echo $filter_status === 'approved' ? 'selected' : ''; ?>>Approved</option>
                            <option value="rejected" <?php echo $filter_status === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                            <option value="cancelled" <?php echo $filter_status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label class="form-label">Cryptocurrency</label>
                        <select name="crypto" class="form-select form-select-sm">
                            <option value="">All Crypto</option>
                            <option value="BTC" <?php echo $filter_crypto === 'BTC' ? 'selected' : ''; ?>>Bitcoin (BTC)</option>
                            <option value="ETH" <?php echo $filter_crypto === 'ETH' ? 'selected' : ''; ?>>Ethereum (ETH)</option>
                            <option value="LTC" <?php echo $filter_crypto === 'LTC' ? 'selected' : ''; ?>>Litecoin (LTC)</option>
                            <option value="BCH" <?php echo $filter_crypto === 'BCH' ? 'selected' : ''; ?>>Bitcoin Cash (BCH)</option>
                            <option value="ADA" <?php echo $filter_crypto === 'ADA' ? 'selected' : ''; ?>>Cardano (ADA)</option>
                            <option value="DOT" <?php echo $filter_crypto === 'DOT' ? 'selected' : ''; ?>>Polkadot (DOT)</option>
                        </select>
                    </div>
                    
                    <div class="col-md-6">
                        <label class="form-label">&nbsp;</label>
                        <div class="btn-group d-block" role="group">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>
                                Filter
                            </button>
                            <a href="crypto-applications.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>
                                Clear
                            </a>
                        </div>
                        
                        <div class="btn-group ms-2" role="group">
                            <a href="crypto-wallets.php" class="btn btn-outline-primary btn-sm">
                                <i class="fab fa-bitcoin me-1"></i>
                                All Wallets
                            </a>
                            <a href="generate-crypto-wallet.php" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                Generate Wallet
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Applications Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-file-alt me-2"></i>
                    Crypto Wallet Applications
                    <span class="badge bg-secondary ms-2"><?php echo number_format($total_records); ?> total</span>
                </h3>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($applications)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table table-sm">
                        <thead>
                            <tr>
                                <th class="w-1">#</th>
                                <th>Applicant</th>
                                <th>Cryptocurrency</th>
                                <th>Type</th>
                                <th>Requested Limit</th>
                                <th>Purpose</th>
                                <th>Status</th>
                                <th>Applied Date</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $row_number = ($current_page - 1) * $records_per_page + 1;
                            foreach ($applications as $app):
                            ?>
                            <tr>
                                <td>
                                    <span class="text-muted"><?php echo $row_number++; ?></span>
                                </td>

                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar avatar-xs me-2">
                                            <?php echo strtoupper(substr($app['first_name'] ?? 'U', 0, 1) . substr($app['last_name'] ?? 'U', 0, 1)); ?>
                                        </div>
                                        <div>
                                            <div class="fw-bold" style="font-size: 0.85rem;"><?php echo htmlspecialchars(($app['first_name'] ?? 'Unknown') . ' ' . ($app['last_name'] ?? 'User')); ?></div>
                                            <small class="text-muted">@<?php echo htmlspecialchars($app['username'] ?? 'unknown'); ?></small>
                                        </div>
                                    </div>
                                </td>

                                <td>
                                    <?php
                                    $crypto_colors = [
                                        'BTC' => 'warning',
                                        'ETH' => 'info',
                                        'LTC' => 'secondary',
                                        'BCH' => 'success',
                                        'ADA' => 'primary',
                                        'DOT' => 'danger'
                                    ];
                                    $crypto_icons = [
                                        'BTC' => 'fab fa-bitcoin',
                                        'ETH' => 'fab fa-ethereum',
                                        'LTC' => 'fas fa-coins',
                                        'BCH' => 'fab fa-bitcoin',
                                        'ADA' => 'fas fa-coins',
                                        'DOT' => 'fas fa-circle'
                                    ];
                                    $color = $crypto_colors[$app['cryptocurrency']] ?? 'secondary';
                                    $icon = $crypto_icons[$app['cryptocurrency']] ?? 'fas fa-coins';
                                    ?>
                                    <span class="badge bg-<?php echo $color; ?> badge-sm">
                                        <i class="<?php echo $icon; ?> me-1"></i>
                                        <?php echo $app['cryptocurrency']; ?>
                                    </span>
                                </td>

                                <td>
                                    <span class="badge bg-primary badge-sm">
                                        <?php echo ucfirst($app['wallet_type']); ?>
                                    </span>
                                </td>

                                <td>
                                    <span class="fw-bold text-primary">
                                        <?php echo number_format($app['requested_limit'], 8); ?> <?php echo $app['cryptocurrency']; ?>
                                    </span>
                                </td>

                                <td>
                                    <div class="text-truncate" style="max-width: 150px;" title="<?php echo htmlspecialchars($app['purpose'] ?? 'Not specified'); ?>">
                                        <?php echo htmlspecialchars($app['purpose'] ?? 'Not specified'); ?>
                                    </div>
                                </td>

                                <td>
                                    <?php
                                    $status_colors = [
                                        'pending' => 'warning',
                                        'approved' => 'success',
                                        'rejected' => 'danger',
                                        'cancelled' => 'secondary'
                                    ];
                                    $status_color = $status_colors[$app['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $status_color; ?> badge-sm">
                                        <?php echo ucfirst($app['status']); ?>
                                    </span>
                                </td>

                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="text-muted" style="font-size: 0.85rem;"><?php echo date('M j, Y', strtotime($app['applied_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($app['applied_at'])); ?></small>
                                    </div>
                                </td>

                                <td>
                                    <div class="btn-list">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewApplication(<?php echo htmlspecialchars(json_encode($app)); ?>)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <?php if ($app['status'] === 'pending'): ?>
                                        <button type="button" class="btn btn-sm btn-outline-success" onclick="reviewApplication(<?php echo $app['application_id']; ?>, 'approve')" title="Approve">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="reviewApplication(<?php echo $app['application_id']; ?>, 'reject')" title="Reject">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-file-alt" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No applications found</p>
                    <p class="empty-subtitle text-muted">
                        No crypto wallet applications have been submitted yet.
                    </p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Application Review Modal -->
<div class="modal modal-blur fade" id="reviewModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <form method="POST" action="">
                <div class="modal-header">
                    <h5 class="modal-title" id="reviewModalTitle">Review Application</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="application_id" id="reviewApplicationId">
                    <input type="hidden" name="action" id="reviewAction">

                    <div class="mb-3">
                        <label class="form-label">Review Notes</label>
                        <textarea name="review_notes" class="form-control" rows="3" placeholder="Enter review notes (optional)..."></textarea>
                        <small class="form-hint">Provide reason for approval/rejection</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn" id="reviewSubmitBtn">Submit Review</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Application Details Modal -->
<div class="modal modal-blur fade" id="applicationModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Application Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="applicationDetails">
                <!-- Application details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function viewApplication(appData) {
    const app = JSON.parse(appData);

    const detailsHtml = `
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Application Information</h3>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-5">Application ID:</dt>
                            <dd class="col-sm-7"><strong>#${app.application_id}</strong></dd>

                            <dt class="col-sm-5">Cryptocurrency:</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-warning">${app.cryptocurrency}</span>
                            </dd>

                            <dt class="col-sm-5">Wallet Type:</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-primary">${app.wallet_type.charAt(0).toUpperCase() + app.wallet_type.slice(1)}</span>
                            </dd>

                            <dt class="col-sm-5">Requested Limit:</dt>
                            <dd class="col-sm-7">
                                <span class="fw-bold text-primary">${parseFloat(app.requested_limit).toFixed(8)} ${app.cryptocurrency}</span>
                            </dd>

                            <dt class="col-sm-5">Status:</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-${getStatusColor(app.status)}">${app.status.charAt(0).toUpperCase() + app.status.slice(1)}</span>
                            </dd>

                            <dt class="col-sm-5">Applied Date:</dt>
                            <dd class="col-sm-7">
                                <div>${formatDate(app.applied_at)}</div>
                                <small class="text-muted">${formatTime(app.applied_at)}</small>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Applicant Details</h3>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-5">Name:</dt>
                            <dd class="col-sm-7">
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2">
                                        ${(app.first_name || 'U').charAt(0).toUpperCase()}${(app.last_name || 'U').charAt(0).toUpperCase()}
                                    </div>
                                    <div>
                                        <div class="fw-bold">${app.first_name || 'Unknown'} ${app.last_name || 'User'}</div>
                                        <small class="text-muted">@${app.username || 'unknown'}</small>
                                    </div>
                                </div>
                            </dd>

                            <dt class="col-sm-5">Account Number:</dt>
                            <dd class="col-sm-7"><code>${app.account_number || 'N/A'}</code></dd>

                            ${app.reviewed_by ? `
                            <dt class="col-sm-5">Reviewed By:</dt>
                            <dd class="col-sm-7">${app.admin_first_name} ${app.admin_last_name}</dd>

                            <dt class="col-sm-5">Review Date:</dt>
                            <dd class="col-sm-7">
                                <div>${formatDate(app.reviewed_at)}</div>
                                <small class="text-muted">${formatTime(app.reviewed_at)}</small>
                            </dd>
                            ` : ''}
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Purpose</h3>
                    </div>
                    <div class="card-body">
                        <p class="mb-0">${app.purpose || 'No purpose specified'}</p>
                    </div>
                </div>

                ${app.review_notes ? `
                <div class="card mt-3">
                    <div class="card-header">
                        <h3 class="card-title">Review Notes</h3>
                    </div>
                    <div class="card-body">
                        <p class="mb-0">${app.review_notes}</p>
                    </div>
                </div>
                ` : ''}
            </div>
        </div>
    `;

    document.getElementById('applicationDetails').innerHTML = detailsHtml;
    const modal = new bootstrap.Modal(document.getElementById('applicationModal'));
    modal.show();
}

function reviewApplication(applicationId, action) {
    document.getElementById('reviewApplicationId').value = applicationId;
    document.getElementById('reviewAction').value = action;

    const title = action === 'approve' ? 'Approve Application' : 'Reject Application';
    const btnClass = action === 'approve' ? 'btn-success' : 'btn-danger';
    const btnText = action === 'approve' ? 'Approve' : 'Reject';

    document.getElementById('reviewModalTitle').textContent = title;
    document.getElementById('reviewSubmitBtn').className = 'btn ' + btnClass;
    document.getElementById('reviewSubmitBtn').textContent = btnText;

    const modal = new bootstrap.Modal(document.getElementById('reviewModal'));
    modal.show();
}

function getStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger',
        'cancelled': 'secondary'
    };
    return colors[status] || 'secondary';
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function formatTime(dateString) {
    return new Date(dateString).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}
</script>

<?php include 'includes/admin-footer.php'; ?>

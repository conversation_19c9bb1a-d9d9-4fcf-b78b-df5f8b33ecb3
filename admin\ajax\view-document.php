<?php
require_once '../../config/config.php';
requireAdmin();

try {
    $document_id = intval($_GET['id'] ?? 0);
    
    if ($document_id <= 0) {
        throw new Exception('Invalid document ID');
    }
    
    $db = getDB();
    
    // Get document information
    $query = "SELECT ud.*, a.first_name, a.last_name 
              FROM user_documents ud 
              LEFT JOIN accounts a ON ud.user_id = a.id 
              WHERE ud.id = ?";
    
    $result = $db->query($query, [$document_id]);
    
    if (!$result || $result->num_rows === 0) {
        throw new Exception('Document not found');
    }
    
    $document = $result->fetch_assoc();
    
    // Build full file path
    $file_path = '../../' . ltrim($document['file_path'], '/');
    
    if (!file_exists($file_path)) {
        throw new Exception('Document file not found on server');
    }
    
    // Get file info
    $file_extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));
    $file_size = filesize($file_path);
    
    // Set appropriate headers based on file type
    switch ($file_extension) {
        case 'pdf':
            header('Content-Type: application/pdf');
            break;
        case 'jpg':
        case 'jpeg':
            header('Content-Type: image/jpeg');
            break;
        case 'png':
            header('Content-Type: image/png');
            break;
        case 'gif':
            header('Content-Type: image/gif');
            break;
        default:
            header('Content-Type: application/octet-stream');
    }
    
    // Set headers for inline display
    header('Content-Length: ' . $file_size);
    header('Content-Disposition: inline; filename="' . basename($document['document_name']) . '"');
    header('Cache-Control: private, max-age=3600');
    header('Pragma: cache');
    
    // Log the view action
    error_log("Document viewed: Document ID {$document_id}, Admin: {$_SESSION['user_id']}");
    
    // Output file content
    readfile($file_path);
    
} catch (Exception $e) {
    error_log("Document view error: " . $e->getMessage());
    
    // Return error page
    header('Content-Type: text/html');
    echo '<!DOCTYPE html>
    <html>
    <head>
        <title>Document Error</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error { color: #dc3545; }
        </style>
    </head>
    <body>
        <h1 class="error">Document Error</h1>
        <p>' . htmlspecialchars($e->getMessage()) . '</p>
        <button onclick="window.close()">Close Window</button>
    </body>
    </html>';
}
?>

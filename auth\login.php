<?php
require_once '../config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
        redirect('../admin/');
    } else {
        redirect('../dashboard/');
    }
}

$error = '';
$username = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    // Check for too many failed attempts
    $failed_attempts = getFailedLoginAttempts($username, $ip_address);
    if ($failed_attempts >= MAX_LOGIN_ATTEMPTS) {
        $error = 'Too many failed login attempts. Please try again later.';
    } else {
        if (empty($username) || empty($password)) {
            $error = 'Please enter both username and password.';
        } else {
            try {
                $db = getDB();
                $sql = "SELECT id, username, password, first_name, last_name, email, account_number,
                              balance, status, is_admin, kyc_status
                        FROM accounts
                        WHERE username = ? AND status = 'active'";
                
                $result = $db->query($sql, [$username]);
                
                if ($result && $result->num_rows === 1) {
                    $user = $result->fetch_assoc();
                    
                    if (verifyPassword($password, $user['password'])) {
                        // Validate email address first
                        if (!isValidEmail($user['email'])) {
                            $error = 'Your account email address is invalid. Please contact support to update your email address before logging in.';
                        } else {
                            // Generate and send OTP
                            $otp = generateOTP();
                            $user_name = $user['first_name'] . ' ' . $user['last_name'];

                            // Store OTP first
                            if (storeOTP($user['id'], $otp)) {
                                // Try to send email
                                $emailSent = sendOTPEmail($user['email'], $otp, $user_name);

                                if ($emailSent) {
                                    // Set OTP session variables
                                    $_SESSION['otp_user_id'] = $user['id'];
                                    $_SESSION['otp_pending'] = true;
                                    $_SESSION['otp_email'] = $user['email'];

                                    // Record successful login attempt
                                    recordLoginAttempt($username, true);

                                    // Log activity
                                    logActivity($user['id'], 'User credentials verified, OTP sent to email');

                                    // Redirect to OTP verification
                                    header('Location: verify-otp.php');
                                    exit();
                                } else {
                                    // Email failed but OTP is stored - still allow verification
                                    $_SESSION['otp_user_id'] = $user['id'];
                                    $_SESSION['otp_pending'] = true;
                                    $_SESSION['otp_email'] = $user['email'];
                                    $_SESSION['email_failed'] = true;

                                    // Record login attempt
                                    recordLoginAttempt($username, true);

                                    // Log activity
                                    logActivity($user['id'], 'User credentials verified, OTP generated but email failed');

                                    // Redirect to OTP verification with email warning
                                    header('Location: verify-otp.php?email_failed=1');
                                    exit();
                                }
                            } else {
                                $error = 'Failed to generate verification code. Please try again.';
                            }
                        }
                    } else {
                        $error = 'Invalid username or password.';
                        recordLoginAttempt($username, false);
                    }
                } else {
                    $error = 'Invalid username or password.';
                    recordLoginAttempt($username, false);
                }
            } catch (Exception $e) {
                error_log("Login error: " . $e->getMessage());
                $error = 'An error occurred. Please try again.';
            }
        }
    }
}

$page_title = 'Login';

// Get appearance settings for bank name
$appearance = getAppearanceSettings();
$bank_name = getBankName();
?>
<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title><?php echo $page_title . ' - ' . $bank_name; ?></title>

    <!-- CSS files -->
    <link rel="stylesheet" href="login-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="login-container">
        <!-- Left Side - Hero Section -->
        <div class="login-hero">
            <div class="hero-content">
                <div class="hero-logo">
                    <div class="hero-logo-icon">
                        <i class="fas fa-university"></i>
                    </div>
                    <div class="hero-logo-text"><?php echo htmlspecialchars($bank_name); ?></div>
                </div>

                <h1 class="hero-title">Welcome back to <?php echo htmlspecialchars($bank_name); ?></h1>
                <p class="hero-subtitle">Build your financial future effortlessly with our powerful banking platform.</p>

                <div class="hero-testimonial">
                    <p class="testimonial-quote">"Simply all the tools that my team and I need."</p>
                    <div class="testimonial-author">Karen Yue</div>
                    <div class="testimonial-role">Director of Digital Banking Technology</div>
                </div>
            </div>
        </div>

        <!-- Right Side - Login Form -->
        <div class="login-form-section">
            <div class="form-header">
                <h1 class="form-title">Welcome back to <?php echo htmlspecialchars($bank_name); ?></h1>
                <p class="form-subtitle">Build your financial future effortlessly with our powerful banking platform.</p>
            </div>

            <?php if (!empty($error)): ?>
            <div class="alert alert-error">
                <?php echo htmlspecialchars($error); ?>
            </div>
            <?php endif; ?>

            <?php if (isset($_GET['timeout'])): ?>
            <div class="alert alert-warning">
                Your session has expired. Please log in again.
            </div>
            <?php endif; ?>

            <form class="login-form" action="" method="post" autocomplete="off" novalidate>
                <div class="form-group">
                    <label class="form-label">Email</label>
                    <input type="text" name="username" class="form-input" placeholder="<EMAIL>"
                           value="<?php echo htmlspecialchars($username); ?>" required autocomplete="username">
                </div>

                <div class="form-group">
                    <label class="form-label">Password</label>
                    <div class="password-input-group">
                        <input type="password" name="password" class="form-input" placeholder="••••••••"
                               required autocomplete="current-password" id="password-input">
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye" id="password-icon"></i>
                        </button>
                    </div>
                    <div class="forgot-password">
                        <a href="forgot-password.php">Forgot password?</a>
                    </div>
                </div>

                <div class="remember-me">
                    <input type="checkbox" class="remember-checkbox" id="remember">
                    <label class="remember-label" for="remember">Remember sign in details</label>
                </div>

                <button type="submit" class="login-button">Log in</button>

                <div class="signup-link">
                    Don't have an account? <a href="register.php">Sign up</a>
                </div>
            </form>
        </div>
    </div>
    <script>
        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password-input');
            const passwordIcon = document.getElementById('password-icon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                passwordIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                passwordIcon.className = 'fas fa-eye';
            }
        }

        // Form submission with loading state
        document.querySelector('.login-form').addEventListener('submit', function() {
            const submitButton = document.querySelector('.login-button');
            submitButton.classList.add('loading');
            submitButton.textContent = 'Signing in...';
            submitButton.disabled = true;
        });

        // Add focus animations
        document.querySelectorAll('.form-input').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });
        });
    </script>
</body>
</html>

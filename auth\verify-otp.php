<?php
require_once '../config/config.php';

// Redirect if not in OTP verification state
if (!isset($_SESSION['otp_user_id']) || !isset($_SESSION['otp_pending'])) {
    redirect('login.php');
}

$error = '';
$success = '';
$email_failed = isset($_SESSION['email_failed']) || isset($_GET['email_failed']);
$user_email = $_SESSION['otp_email'] ?? 'your email';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $otp = sanitizeInput($_POST['otp'] ?? '');
    $user_id = $_SESSION['otp_user_id'];
    
    if (empty($otp)) {
        $error = 'Please enter the verification code.';
    } elseif (strlen($otp) !== 6 || !ctype_digit($otp)) {
        $error = 'Please enter a valid 6-digit verification code.';
    } else {
        if (verifyOTP($user_id, $otp)) {
            // OTP verified successfully - complete login
            try {
                $db = getDB();
                $sql = "SELECT id, username, first_name, last_name, email, account_number,
                              balance, status, is_admin, kyc_status
                        FROM accounts WHERE id = ? AND status = 'active'";
                
                $result = $db->query($sql, [$user_id]);
                
                if ($result && $result->num_rows === 1) {
                    $user = $result->fetch_assoc();
                    
                    // Set session variables
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['first_name'] = $user['first_name'];
                    $_SESSION['last_name'] = $user['last_name'];
                    $_SESSION['email'] = $user['email'];
                    $_SESSION['account_number'] = $user['account_number'];
                    $_SESSION['balance'] = $user['balance'];
                    $_SESSION['is_admin'] = (bool)$user['is_admin'];
                    $_SESSION['kyc_status'] = $user['kyc_status'];
                    $_SESSION['last_activity'] = time();
                    
                    // Clear OTP session variables
                    unset($_SESSION['otp_user_id']);
                    unset($_SESSION['otp_pending']);
                    
                    // Update last login time
                    $db->query("UPDATE accounts SET last_login = NOW() WHERE id = ?", [$user['id']]);
                    
                    // Log activity
                    logActivity($user['id'], 'User completed OTP verification and logged in');

                    // Redirect based on user type
                    if ($user['is_admin']) {
                        redirect('../admin/');
                    } else {
                        redirect('../dashboard/');
                    }
                } else {
                    $error = 'Account not found or inactive.';
                }
            } catch (Exception $e) {
                error_log("OTP verification error: " . $e->getMessage());
                $error = 'An error occurred. Please try again.';
            }
        } else {
            $error = 'Invalid or expired verification code. Please try again.';
        }
    }
}

// Handle resend OTP request
if (isset($_GET['resend']) && $_GET['resend'] === '1') {
    try {
        $user_id = $_SESSION['otp_user_id'];
        $db = getDB();
        $sql = "SELECT email, first_name, last_name FROM accounts WHERE id = ?";
        $result = $db->query($sql, [$user_id]);

        if ($result && $result->num_rows === 1) {
            $user = $result->fetch_assoc();
            $otp = generateOTP();

            if (storeOTP($user_id, $otp)) {
                $emailSent = sendOTPEmail($user['email'], $otp, $user['first_name'] . ' ' . $user['last_name']);

                if ($emailSent) {
                    $success = 'A new verification code has been sent to your email.';
                    unset($_SESSION['email_failed']); // Clear email failed flag
                } else {
                    $_SESSION['email_failed'] = true;
                    $success = 'A new verification code has been generated. Since email delivery failed, please contact support or check with an administrator for the code.';
                }
            } else {
                $error = 'Failed to generate verification code. Please try again.';
            }
        }
    } catch (Exception $e) {
        error_log("Resend OTP error: " . $e->getMessage());
        $error = 'An error occurred. Please try again.';
    }
}

$page_title = 'Verify Your Identity';
$bank_name = getBankName();
?>
<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title><?php echo $page_title . ' - ' . $bank_name; ?></title>

    <!-- CSS files -->
    <link rel="stylesheet" href="otp-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="otp-container">
        <div class="otp-card">
            <div class="otp-header">
                <div class="otp-logo">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h1 class="otp-title">Verify Your Identity</h1>
                <?php if ($email_failed): ?>
                <p class="otp-subtitle">
                    <strong>Email delivery failed!</strong> We couldn't send the verification code to <strong><?php echo htmlspecialchars($user_email); ?></strong>.
                    Please contact support for assistance, or check with an administrator who can provide you with the current verification code.
                </p>
                <?php else: ?>
                <p class="otp-subtitle">We've sent a 6-digit verification code to <strong><?php echo htmlspecialchars($user_email); ?></strong>. Please enter it below to complete your login.</p>
                <?php endif; ?>
            </div>

            <?php if ($email_failed): ?>
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Email Delivery Issue:</strong> The verification code couldn't be sent to your email.
                You can still proceed by getting the code from an administrator or contacting support.
            </div>
            <?php endif; ?>

            <?php if (!empty($error)): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?php echo htmlspecialchars($success); ?>
            </div>
            <?php endif; ?>

            <form class="otp-form" action="" method="post" autocomplete="off">
                <div class="otp-input-group">
                    <label class="otp-label">Enter Verification Code</label>
                    <div class="otp-inputs">
                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" autocomplete="off">
                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" autocomplete="off">
                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" autocomplete="off">
                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" autocomplete="off">
                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" autocomplete="off">
                        <input type="text" class="otp-input" maxlength="1" pattern="[0-9]" autocomplete="off">
                    </div>
                    <input type="hidden" name="otp" id="otp-hidden">
                </div>

                <button type="submit" class="verify-button" id="verify-btn" disabled>
                    <i class="fas fa-check"></i>
                    Verify Code
                </button>
            </form>

            <div class="otp-actions">
                <p class="resend-text">Didn't receive the code?</p>
                <a href="?resend=1" class="resend-link">
                    <i class="fas fa-redo"></i>
                    Resend Code
                </a>
            </div>

            <div class="back-to-login">
                <a href="login.php">
                    <i class="fas fa-arrow-left"></i>
                    Back to Login
                </a>
            </div>
        </div>
    </div>

    <script>
        // OTP input handling
        const otpInputs = document.querySelectorAll('.otp-input');
        const otpHidden = document.getElementById('otp-hidden');
        const verifyBtn = document.getElementById('verify-btn');

        otpInputs.forEach((input, index) => {
            input.addEventListener('input', function(e) {
                const value = e.target.value;
                
                // Only allow digits
                if (!/^\d$/.test(value)) {
                    e.target.value = '';
                    return;
                }

                // Move to next input
                if (value && index < otpInputs.length - 1) {
                    otpInputs[index + 1].focus();
                }

                updateOTPValue();
            });

            input.addEventListener('keydown', function(e) {
                // Handle backspace
                if (e.key === 'Backspace' && !e.target.value && index > 0) {
                    otpInputs[index - 1].focus();
                }
                
                // Handle paste
                if (e.key === 'v' && (e.ctrlKey || e.metaKey)) {
                    e.preventDefault();
                    navigator.clipboard.readText().then(text => {
                        const digits = text.replace(/\D/g, '').slice(0, 6);
                        digits.split('').forEach((digit, i) => {
                            if (otpInputs[i]) {
                                otpInputs[i].value = digit;
                            }
                        });
                        updateOTPValue();
                        if (digits.length === 6) {
                            otpInputs[5].focus();
                        }
                    });
                }
            });

            input.addEventListener('focus', function() {
                this.select();
            });
        });

        function updateOTPValue() {
            const otp = Array.from(otpInputs).map(input => input.value).join('');
            otpHidden.value = otp;
            verifyBtn.disabled = otp.length !== 6;
            
            if (otp.length === 6) {
                verifyBtn.classList.add('ready');
            } else {
                verifyBtn.classList.remove('ready');
            }
        }

        // Auto-submit when all digits are entered
        function checkAutoSubmit() {
            const otp = Array.from(otpInputs).map(input => input.value).join('');
            if (otp.length === 6) {
                setTimeout(() => {
                    document.querySelector('.otp-form').submit();
                }, 500);
            }
        }

        otpInputs.forEach(input => {
            input.addEventListener('input', checkAutoSubmit);
        });

        // Focus first input on load
        otpInputs[0].focus();

        // Form submission with loading state
        document.querySelector('.otp-form').addEventListener('submit', function() {
            verifyBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Verifying...';
            verifyBtn.disabled = true;
        });
    </script>
</body>
</html>

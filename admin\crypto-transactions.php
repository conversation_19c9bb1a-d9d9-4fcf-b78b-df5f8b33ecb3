<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Crypto Transactions';

// Pagination settings
$records_per_page = 25;
$current_page = intval($_GET['page'] ?? 1);
$offset = ($current_page - 1) * $records_per_page;

// Filter parameters
$filter_type = $_GET['type'] ?? '';
$filter_status = $_GET['status'] ?? '';
$filter_crypto = $_GET['crypto'] ?? '';
$filter_wallet = $_GET['wallet'] ?? '';
$filter_date_from = $_GET['date_from'] ?? '';
$filter_date_to = $_GET['date_to'] ?? '';
$filter_amount_min = floatval($_GET['amount_min'] ?? 0);
$filter_amount_max = floatval($_GET['amount_max'] ?? 0);

// Build WHERE clause for filters
$where_conditions = [];
$params = [];

if (!empty($filter_type)) {
    $where_conditions[] = "ct.transaction_type = ?";
    $params[] = $filter_type;
}

if (!empty($filter_status)) {
    $where_conditions[] = "ct.status = ?";
    $params[] = $filter_status;
}

if (!empty($filter_crypto)) {
    $where_conditions[] = "ct.cryptocurrency = ?";
    $params[] = $filter_crypto;
}

if (!empty($filter_wallet)) {
    $where_conditions[] = "(cw.wallet_name LIKE ? OR cw.wallet_address LIKE ?)";
    $search_term = "%$filter_wallet%";
    $params[] = $search_term;
    $params[] = $search_term;
}

if (!empty($filter_date_from)) {
    $where_conditions[] = "DATE(ct.created_at) >= ?";
    $params[] = $filter_date_from;
}

if (!empty($filter_date_to)) {
    $where_conditions[] = "DATE(ct.created_at) <= ?";
    $params[] = $filter_date_to;
}

if ($filter_amount_min > 0) {
    $where_conditions[] = "ct.amount >= ?";
    $params[] = $filter_amount_min;
}

if ($filter_amount_max > 0) {
    $where_conditions[] = "ct.amount <= ?";
    $params[] = $filter_amount_max;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

try {
    $db = getDB();
    
    // Get total count for pagination
    $count_query = "SELECT COUNT(*) as total 
                    FROM crypto_transactions ct 
                    LEFT JOIN crypto_wallets cw ON ct.wallet_id = cw.wallet_id 
                    LEFT JOIN accounts a ON ct.account_id = a.id 
                    $where_clause";
    $count_result = $db->query($count_query, $params);
    $total_records = $count_result->fetch_assoc()['total'];
    $total_pages = ceil($total_records / $records_per_page);
    
    // Get transactions with pagination
    $transactions_query = "SELECT ct.*, 
                          cw.wallet_name, cw.wallet_address,
                          a.first_name, a.last_name, a.username, a.account_number,
                          admin.first_name as admin_first_name, admin.last_name as admin_last_name
                          FROM crypto_transactions ct 
                          LEFT JOIN crypto_wallets cw ON ct.wallet_id = cw.wallet_id 
                          LEFT JOIN accounts a ON ct.account_id = a.id 
                          LEFT JOIN accounts admin ON ct.processed_by = admin.id 
                          $where_clause
                          ORDER BY ct.created_at DESC 
                          LIMIT $records_per_page OFFSET $offset";
    
    $transactions_result = $db->query($transactions_query, $params);
    $transactions = [];
    while ($row = $transactions_result->fetch_assoc()) {
        $transactions[] = $row;
    }
    
} catch (Exception $e) {
    $error = "Failed to load crypto transactions: " . $e->getMessage();
    $transactions = [];
    $total_records = 0;
    $total_pages = 0;
}

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="crypto-wallets.php">Crypto Wallets</a></li>
        <li class="breadcrumb-item active" aria-current="page">Crypto Transactions</li>
    </ol>
</nav>

<!-- Error Message -->
<?php if (isset($error)): ?>
<div class="alert alert-danger" role="alert">
    <div class="d-flex">
        <div><i class="fas fa-exclamation-circle me-2"></i></div>
        <div>
            <h4 class="alert-title">Error!</h4>
            <div class="text-muted"><?php echo htmlspecialchars($error); ?></div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Filters Card -->
<div class="row row-cards mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-filter me-2"></i>
                    Transaction Filters
                </h3>
                <div class="card-actions">
                    <a href="crypto-transactions.php" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-times me-2"></i>
                        Clear Filters
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form method="GET" action="" class="row g-2">
                    <div class="col-md-2">
                        <label class="form-label">Type</label>
                        <select name="type" class="form-select form-select-sm">
                            <option value="">All Types</option>
                            <option value="credit" <?php echo $filter_type === 'credit' ? 'selected' : ''; ?>>Credit</option>
                            <option value="debit" <?php echo $filter_type === 'debit' ? 'selected' : ''; ?>>Debit</option>
                            <option value="send" <?php echo $filter_type === 'send' ? 'selected' : ''; ?>>Send</option>
                            <option value="receive" <?php echo $filter_type === 'receive' ? 'selected' : ''; ?>>Receive</option>
                            <option value="mining" <?php echo $filter_type === 'mining' ? 'selected' : ''; ?>>Mining</option>
                            <option value="staking" <?php echo $filter_type === 'staking' ? 'selected' : ''; ?>>Staking</option>
                            <option value="fee" <?php echo $filter_type === 'fee' ? 'selected' : ''; ?>>Fee</option>
                            <option value="exchange" <?php echo $filter_type === 'exchange' ? 'selected' : ''; ?>>Exchange</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select form-select-sm">
                            <option value="">All Status</option>
                            <option value="pending" <?php echo $filter_status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                            <option value="processing" <?php echo $filter_status === 'processing' ? 'selected' : ''; ?>>Processing</option>
                            <option value="confirmed" <?php echo $filter_status === 'confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                            <option value="failed" <?php echo $filter_status === 'failed' ? 'selected' : ''; ?>>Failed</option>
                            <option value="cancelled" <?php echo $filter_status === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Cryptocurrency</label>
                        <select name="crypto" class="form-select form-select-sm">
                            <option value="">All Crypto</option>
                            <option value="BTC" <?php echo $filter_crypto === 'BTC' ? 'selected' : ''; ?>>Bitcoin (BTC)</option>
                            <option value="ETH" <?php echo $filter_crypto === 'ETH' ? 'selected' : ''; ?>>Ethereum (ETH)</option>
                            <option value="LTC" <?php echo $filter_crypto === 'LTC' ? 'selected' : ''; ?>>Litecoin (LTC)</option>
                            <option value="BCH" <?php echo $filter_crypto === 'BCH' ? 'selected' : ''; ?>>Bitcoin Cash (BCH)</option>
                            <option value="ADA" <?php echo $filter_crypto === 'ADA' ? 'selected' : ''; ?>>Cardano (ADA)</option>
                            <option value="DOT" <?php echo $filter_crypto === 'DOT' ? 'selected' : ''; ?>>Polkadot (DOT)</option>
                        </select>
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Wallet</label>
                        <input type="text" name="wallet" class="form-control form-control-sm" placeholder="Wallet name/address..." value="<?php echo htmlspecialchars($filter_wallet); ?>">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Date From</label>
                        <input type="date" name="date_from" class="form-control form-control-sm" value="<?php echo htmlspecialchars($filter_date_from); ?>">
                    </div>
                    
                    <div class="col-md-2">
                        <label class="form-label">Date To</label>
                        <input type="date" name="date_to" class="form-control form-control-sm" value="<?php echo htmlspecialchars($filter_date_to); ?>">
                    </div>
                    
                    <div class="col-md-6">
                        <label class="form-label">Amount Range</label>
                        <div class="input-group input-group-sm">
                            <input type="number" name="amount_min" class="form-control" step="0.00000001" placeholder="Min" value="<?php echo $filter_amount_min > 0 ? $filter_amount_min : ''; ?>">
                            <span class="input-group-text">-</span>
                            <input type="number" name="amount_max" class="form-control" step="0.00000001" placeholder="Max" value="<?php echo $filter_amount_max > 0 ? $filter_amount_max : ''; ?>">
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <label class="form-label">&nbsp;</label>
                        <div class="btn-group d-block" role="group">
                            <button type="submit" class="btn btn-primary btn-sm">
                                <i class="fas fa-search me-1"></i>
                                Filter
                            </button>
                            <a href="crypto-transactions.php" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-times me-1"></i>
                                Clear
                            </a>
                        </div>
                        
                        <div class="btn-group ms-2" role="group">
                            <a href="crypto-operations.php" class="btn btn-success btn-sm">
                                <i class="fas fa-plus me-1"></i>
                                New Operation
                            </a>
                            <a href="crypto-wallets.php" class="btn btn-outline-primary btn-sm">
                                <i class="fab fa-bitcoin me-1"></i>
                                All Wallets
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Crypto Transactions Table -->
<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-list me-2"></i>
                    Cryptocurrency Transactions
                    <span class="badge bg-secondary ms-2"><?php echo number_format($total_records); ?> total</span>
                </h3>
                <div class="card-actions">
                    <span class="text-muted">
                        Showing <?php echo number_format(($current_page - 1) * $records_per_page + 1); ?> -
                        <?php echo number_format(min($current_page * $records_per_page, $total_records)); ?>
                        of <?php echo number_format($total_records); ?> transactions
                    </span>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (!empty($transactions)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter card-table table-sm">
                        <thead>
                            <tr>
                                <th class="w-1">#</th>
                                <th>Transaction ID</th>
                                <th>Wallet</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th class="w-1">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $row_number = ($current_page - 1) * $records_per_page + 1;
                            foreach ($transactions as $transaction):
                            ?>
                            <tr>
                                <!-- Row Number -->
                                <td>
                                    <span class="text-muted"><?php echo $row_number++; ?></span>
                                </td>

                                <!-- Transaction ID -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="fw-bold">#<?php echo $transaction['id']; ?></div>
                                        <small class="text-muted font-monospace"><?php echo htmlspecialchars(substr($transaction['transaction_hash'], 0, 8) . '...' . substr($transaction['transaction_hash'], -8)); ?></small>
                                    </div>
                                </td>

                                <!-- Wallet -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="fw-bold font-monospace"><?php echo substr($transaction['wallet_address'], 0, 8) . '...' . substr($transaction['wallet_address'], -8); ?></div>
                                        <small class="text-muted"><?php echo htmlspecialchars($transaction['wallet_name']); ?></small>
                                    </div>
                                </td>

                                <!-- Type -->
                                <td>
                                    <?php
                                    $type_colors = [
                                        'credit' => 'success',
                                        'debit' => 'danger',
                                        'send' => 'warning',
                                        'receive' => 'info',
                                        'mining' => 'primary',
                                        'staking' => 'secondary',
                                        'fee' => 'dark',
                                        'exchange' => 'purple'
                                    ];
                                    $color = $type_colors[$transaction['transaction_type']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $color; ?> badge-sm">
                                        <?php echo ucfirst($transaction['transaction_type']); ?>
                                    </span>
                                </td>

                                <!-- Amount -->
                                <td>
                                    <span class="fw-bold <?php echo in_array($transaction['transaction_type'], ['credit', 'receive', 'mining', 'staking']) ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo in_array($transaction['transaction_type'], ['credit', 'receive', 'mining', 'staking']) ? '+' : '-'; ?>
                                        <?php echo number_format($transaction['amount'], 8); ?> <?php echo $transaction['cryptocurrency']; ?>
                                    </span>
                                    <?php if ($transaction['network_fee'] > 0): ?>
                                    <br><small class="text-muted">Fee: <?php echo number_format($transaction['network_fee'], 8); ?> <?php echo $transaction['cryptocurrency']; ?></small>
                                    <?php endif; ?>
                                </td>

                                <!-- Description -->
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($transaction['description']); ?>">
                                        <?php echo htmlspecialchars($transaction['description']); ?>
                                    </div>
                                    <?php if ($transaction['from_address'] || $transaction['to_address']): ?>
                                    <small class="text-muted d-block">
                                        <?php if ($transaction['from_address']): ?>From: <?php echo substr($transaction['from_address'], 0, 8) . '...'; ?><br><?php endif; ?>
                                        <?php if ($transaction['to_address']): ?>To: <?php echo substr($transaction['to_address'], 0, 8) . '...'; ?><?php endif; ?>
                                    </small>
                                    <?php endif; ?>
                                </td>

                                <!-- Status -->
                                <td>
                                    <?php
                                    $status_colors = [
                                        'pending' => 'warning',
                                        'processing' => 'info',
                                        'confirmed' => 'success',
                                        'failed' => 'danger',
                                        'cancelled' => 'secondary'
                                    ];
                                    $status_color = $status_colors[$transaction['status']] ?? 'secondary';
                                    ?>
                                    <span class="badge bg-<?php echo $status_color; ?> badge-sm">
                                        <?php echo ucfirst($transaction['status']); ?>
                                    </span>
                                    <?php if ($transaction['confirmations'] > 0): ?>
                                    <br><small class="text-muted"><?php echo $transaction['confirmations']; ?> confirmations</small>
                                    <?php endif; ?>
                                </td>

                                <!-- Date -->
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="text-muted" style="font-size: 0.85rem;"><?php echo date('M j, Y', strtotime($transaction['created_at'])); ?></div>
                                        <small class="text-muted"><?php echo date('g:i A', strtotime($transaction['created_at'])); ?></small>
                                    </div>
                                </td>

                                <!-- Actions -->
                                <td>
                                    <div class="btn-list">
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewCryptoTransaction(<?php echo htmlspecialchars(json_encode($transaction)); ?>)" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-search" style="font-size: 3rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">No crypto transactions found</p>
                    <p class="empty-subtitle text-muted">
                        <?php if (!empty(array_filter([$filter_type, $filter_status, $filter_crypto, $filter_wallet, $filter_date_from, $filter_date_to]))): ?>
                        Try adjusting your filters to see more results.
                        <?php else: ?>
                        No cryptocurrency transactions have been recorded yet.
                        <?php endif; ?>
                    </p>
                    <div class="empty-action">
                        <a href="crypto-operations.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            Create First Transaction
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Crypto Transaction Details Modal -->
<div class="modal modal-blur fade" id="cryptoTransactionModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Crypto Transaction Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="cryptoTransactionDetails">
                <!-- Transaction details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="printCryptoTransaction()">
                    <i class="fas fa-print me-2"></i>
                    Print
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Crypto transaction management functions
function viewCryptoTransaction(transactionData) {
    const transaction = JSON.parse(transactionData);

    // Format the transaction details
    const detailsHtml = `
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Transaction Information</h3>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-4">Transaction ID:</dt>
                            <dd class="col-sm-8"><strong>#${transaction.id}</strong></dd>

                            <dt class="col-sm-4">Hash:</dt>
                            <dd class="col-sm-8"><code class="font-monospace">${transaction.transaction_hash}</code></dd>

                            <dt class="col-sm-4">Type:</dt>
                            <dd class="col-sm-8">
                                <span class="badge bg-${getCryptoTransactionTypeColor(transaction.transaction_type)}">
                                    ${formatCryptoTransactionType(transaction.transaction_type)}
                                </span>
                            </dd>

                            <dt class="col-sm-4">Amount:</dt>
                            <dd class="col-sm-8">
                                <span class="fw-bold ${getCryptoTransactionAmountColor(transaction.transaction_type)} fs-4">
                                    ${getCryptoTransactionAmountPrefix(transaction.transaction_type)}${parseFloat(transaction.amount).toFixed(8)} ${transaction.cryptocurrency}
                                </span>
                            </dd>

                            <dt class="col-sm-4">Network Fee:</dt>
                            <dd class="col-sm-8">${parseFloat(transaction.network_fee || 0).toFixed(8)} ${transaction.cryptocurrency}</dd>

                            <dt class="col-sm-4">Status:</dt>
                            <dd class="col-sm-8">
                                <span class="badge bg-${getCryptoTransactionStatusColor(transaction.status)}">
                                    ${transaction.status.charAt(0).toUpperCase() + transaction.status.slice(1)}
                                </span>
                            </dd>

                            <dt class="col-sm-4">Confirmations:</dt>
                            <dd class="col-sm-8">${transaction.confirmations || 0}</dd>
                        </dl>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Wallet & Account Details</h3>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-4">Wallet:</dt>
                            <dd class="col-sm-8">
                                <div class="fw-bold">${transaction.wallet_name}</div>
                                <code class="font-monospace">${transaction.wallet_address}</code>
                            </dd>

                            <dt class="col-sm-4">Account Holder:</dt>
                            <dd class="col-sm-8">
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2">
                                        ${(transaction.first_name || 'U').charAt(0).toUpperCase()}${(transaction.last_name || 'U').charAt(0).toUpperCase()}
                                    </div>
                                    <div>
                                        <div class="fw-bold">${transaction.first_name || 'Unknown'} ${transaction.last_name || 'User'}</div>
                                        <small class="text-muted">@${transaction.username || 'unknown'}</small>
                                    </div>
                                </div>
                            </dd>

                            <dt class="col-sm-4">Account Number:</dt>
                            <dd class="col-sm-8"><code>${transaction.account_number || 'N/A'}</code></dd>

                            ${transaction.from_address ? `
                            <dt class="col-sm-4">From Address:</dt>
                            <dd class="col-sm-8"><code class="font-monospace">${transaction.from_address}</code></dd>
                            ` : ''}

                            ${transaction.to_address ? `
                            <dt class="col-sm-4">To Address:</dt>
                            <dd class="col-sm-8"><code class="font-monospace">${transaction.to_address}</code></dd>
                            ` : ''}

                            <dt class="col-sm-4">Processed By:</dt>
                            <dd class="col-sm-8">
                                ${transaction.admin_first_name ?
                                    `${transaction.admin_first_name} ${transaction.admin_last_name}` :
                                    '<span class="text-muted">System</span>'}
                            </dd>

                            <dt class="col-sm-4">Created:</dt>
                            <dd class="col-sm-8">
                                <div>${formatDate(transaction.created_at)}</div>
                                <small class="text-muted">${formatTime(transaction.created_at)}</small>
                            </dd>

                            <dt class="col-sm-4">Last Updated:</dt>
                            <dd class="col-sm-8">
                                <div>${formatDate(transaction.updated_at)}</div>
                                <small class="text-muted">${formatTime(transaction.updated_at)}</small>
                                ${transaction.updated_at !== transaction.created_at ?
                                    '<small class="badge bg-warning ms-2">Modified</small>' : ''}
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Description</h3>
                    </div>
                    <div class="card-body">
                        <p class="mb-0">${transaction.description}</p>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Load details into modal and show it
    document.getElementById('cryptoTransactionDetails').innerHTML = detailsHtml;
    const modal = new bootstrap.Modal(document.getElementById('cryptoTransactionModal'));
    modal.show();
}

// Helper functions for crypto transaction modal
function getCryptoTransactionTypeColor(type) {
    const colors = {
        'credit': 'success',
        'debit': 'danger',
        'send': 'warning',
        'receive': 'info',
        'mining': 'primary',
        'staking': 'secondary',
        'fee': 'dark',
        'exchange': 'purple'
    };
    return colors[type] || 'secondary';
}

function getCryptoTransactionStatusColor(status) {
    const colors = {
        'pending': 'warning',
        'processing': 'info',
        'confirmed': 'success',
        'failed': 'danger',
        'cancelled': 'secondary'
    };
    return colors[status] || 'secondary';
}

function getCryptoTransactionAmountColor(type) {
    return ['credit', 'receive', 'mining', 'staking'].includes(type) ? 'text-success' : 'text-danger';
}

function getCryptoTransactionAmountPrefix(type) {
    return ['credit', 'receive', 'mining', 'staking'].includes(type) ? '+' : '-';
}

function formatCryptoTransactionType(type) {
    const types = {
        'credit': 'Credit',
        'debit': 'Debit',
        'send': 'Send',
        'receive': 'Receive',
        'mining': 'Mining',
        'staking': 'Staking',
        'fee': 'Fee',
        'exchange': 'Exchange'
    };
    return types[type] || type.charAt(0).toUpperCase() + type.slice(1);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function formatTime(dateString) {
    return new Date(dateString).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
    });
}

function printCryptoTransaction() {
    window.print();
}
</script>

<?php include 'includes/admin-footer.php'; ?>

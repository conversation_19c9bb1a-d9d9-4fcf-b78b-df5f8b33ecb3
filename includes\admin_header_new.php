<?php
/**
 * Admin Header - Administrative interface header with full admin functionality
 * This header is only for admin users and contains all administrative features
 */

// Ensure user is logged in and is admin
if (!isLoggedIn() || !isAdmin()) {
    redirect('auth/login.php');
}

// Get appearance settings
$appearance = getAppearanceSettings();
$bank_name = getBankName();
$theme = $appearance['theme'] ?? 'light';
$color_scheme = $appearance['color_scheme'] ?? 'blue';
$header_fixed = ($appearance['header_fixed'] ?? 'true') === 'true';
$animations_enabled = ($appearance['animations_enabled'] ?? 'true') === 'true';

// Get admin information
$admin_name = $_SESSION['first_name'] . ' ' . $_SESSION['last_name'];
$admin_email = $_SESSION['email'] ?? '';
$admin_avatar = strtoupper(substr($_SESSION['first_name'], 0, 1) . substr($_SESSION['last_name'], 0, 1));
?>
<!DOCTYPE html>
<html lang="en" data-theme="<?php echo htmlspecialchars($theme); ?>" data-color-scheme="<?php echo htmlspecialchars($color_scheme); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? htmlspecialchars($page_title) . ' - Admin - ' : 'Admin - '; ?><?php echo htmlspecialchars($bank_name); ?></title>
    
    <!-- Favicon -->
    <?php if (!empty($appearance['favicon_url'])): ?>
    <link rel="icon" type="image/x-icon" href="<?php echo htmlspecialchars($appearance['favicon_url']); ?>">
    <?php else: ?>
    <link rel="icon" type="image/x-icon" href="<?php echo asset('favicon.ico'); ?>">
    <?php endif; ?>
    
    <!-- CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.3.2/dist/css/tabler.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@tabler/icons@1.39.1/icons-sprite.svg" rel="preload" as="image">
    
    <!-- Font Awesome for additional icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js for admin dashboards -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom admin styles -->
    <style>
        :root {
            --admin-primary: #dc3545;
            --admin-secondary: #c82333;
            --bank-primary: <?php echo $color_scheme === 'blue' ? '#0054a6' : ($color_scheme === 'green' ? '#2e7d32' : '#d32f2f'); ?>;
            --bank-secondary: <?php echo $color_scheme === 'blue' ? '#0066cc' : ($color_scheme === 'green' ? '#388e3c' : '#f44336'); ?>;
        }
        
        <?php if ($theme === 'dark'): ?>
        .navbar-dark {
            background-color: #1a1a1a !important;
            border-bottom: 1px solid #333;
        }
        
        .dropdown-menu {
            background-color: #2d2d2d;
            border-color: #444;
        }
        
        .dropdown-item {
            color: #e0e0e0;
        }
        
        .dropdown-item:hover {
            background-color: #404040;
            color: #fff;
        }
        <?php endif; ?>
        
        .navbar-brand {
            font-weight: 600;
            color: var(--bank-primary) !important;
        }
        
        .admin-badge {
            background: var(--admin-primary);
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 3px;
            margin-left: 8px;
            font-weight: 600;
        }
        
        .bank-logo {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, var(--bank-primary) 0%, var(--bank-secondary) 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.5rem;
        }
        
        <?php if (!$animations_enabled): ?>
        * {
            animation: none !important;
            transition: none !important;
        }
        <?php endif; ?>
        
        .admin-avatar {
            width: 32px;
            height: 32px;
            background: var(--admin-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 14px;
            border: 2px solid var(--admin-secondary);
        }
        
        .navbar-nav .nav-link {
            color: #495057 !important;
            font-weight: 500;
        }
        
        .navbar-nav .nav-link:hover {
            color: var(--admin-primary) !important;
        }
        
        .dropdown-toggle::after {
            display: none;
        }
        
        .admin-nav-item {
            border-left: 3px solid var(--admin-primary);
            padding-left: 0.75rem !important;
        }
        
        .quick-actions {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
            border-radius: 8px;
            padding: 0.5rem;
        }
        
        .quick-actions a {
            color: white !important;
            text-decoration: none;
            font-size: 12px;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        
        .quick-actions a:hover {
            background-color: rgba(255,255,255,0.2);
        }
        
        .admin-alert {
            background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
            color: white;
            border: none;
        }
    </style>
</head>
<body class="<?php echo $theme === 'dark' ? 'theme-dark' : ''; ?>">
    
    <!-- Navigation Header -->
    <header class="navbar navbar-expand-md navbar-light d-print-none <?php echo $header_fixed ? 'navbar-sticky' : ''; ?>">
        <div class="container-xl">
            <!-- Mobile menu toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <!-- Brand -->
            <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
                <a href="<?php echo url('admin/dashboard.php'); ?>" class="d-flex align-items-center text-decoration-none">
                    <?php if (!empty($appearance['logo_url'])): ?>
                        <img src="<?php echo htmlspecialchars($appearance['logo_url']); ?>" alt="<?php echo htmlspecialchars($bank_name); ?>" class="bank-logo" style="background: none;">
                    <?php else: ?>
                        <div class="bank-logo">
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <path d="M3 21l18 0"/>
                                <path d="M3 10l18 0"/>
                                <path d="M5 6l7 -3l7 3"/>
                                <path d="M4 10l0 11"/>
                                <path d="M20 10l0 11"/>
                                <path d="M8 14l0 3"/>
                                <path d="M12 14l0 3"/>
                                <path d="M16 14l0 3"/>
                            </svg>
                        </div>
                    <?php endif; ?>
                    <?php echo htmlspecialchars($bank_name); ?>
                    <span class="admin-badge">ADMIN</span>
                </a>
            </h1>
            
            <!-- Admin Menu -->
            <div class="navbar-nav flex-row order-md-last">
                <!-- Quick Actions -->
                <div class="nav-item dropdown me-3">
                    <a href="#" class="nav-link" data-bs-toggle="dropdown" aria-label="Quick actions">
                        <i class="fas fa-bolt text-warning"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow quick-actions">
                        <div class="d-flex flex-column gap-1">
                            <a href="<?php echo url('admin/users/add.php'); ?>">
                                <i class="fas fa-user-plus me-1"></i> Add User
                            </a>
                            <a href="<?php echo url('admin/transactions/add.php'); ?>">
                                <i class="fas fa-plus me-1"></i> New Transaction
                            </a>
                            <a href="<?php echo url('admin/reports/generate.php'); ?>">
                                <i class="fas fa-chart-bar me-1"></i> Generate Report
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Admin Profile Menu -->
                <div class="nav-item dropdown">
                    <a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown" aria-label="Open admin menu">
                        <div class="admin-avatar">
                            <?php echo htmlspecialchars($admin_avatar); ?>
                        </div>
                        <div class="d-none d-xl-block ps-2">
                            <div><?php echo htmlspecialchars($admin_name); ?></div>
                            <div class="mt-1 small text-muted">Administrator</div>
                        </div>
                    </a>
                    <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
                        <a href="<?php echo url('admin/profile.php'); ?>" class="dropdown-item">
                            <i class="fas fa-user me-2"></i>
                            Admin Profile
                        </a>
                        <a href="<?php echo url('admin/settings/'); ?>" class="dropdown-item">
                            <i class="fas fa-cogs me-2"></i>
                            System Settings
                        </a>
                        <a href="<?php echo url('admin/logs/'); ?>" class="dropdown-item">
                            <i class="fas fa-clipboard-list me-2"></i>
                            Activity Logs
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="<?php echo url('dashboard.php'); ?>" class="dropdown-item">
                            <i class="fas fa-eye me-2"></i>
                            View as User
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="<?php echo url('admin/logout.php'); ?>" class="dropdown-item text-danger">
                            <i class="fas fa-sign-out-alt me-2"></i>
                            Logout
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="navbar-menu">
                <div class="d-flex flex-column flex-md-row flex-fill align-items-stretch align-items-md-center">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link admin-nav-item <?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/dashboard') !== false || (basename($_SERVER['PHP_SELF']) == 'index.php' && strpos($_SERVER['REQUEST_URI'], '/admin') !== false)) ? 'active' : ''; ?>" href="<?php echo url('admin/dashboard.php'); ?>">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Admin Dashboard
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/users') !== false) ? 'active' : ''; ?>" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-users me-2"></i>
                                User Management
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="<?php echo url('admin/users/'); ?>">
                                    <i class="fas fa-list me-2"></i>All Users
                                </a>
                                <a class="dropdown-item" href="<?php echo url('admin/users/add.php'); ?>">
                                    <i class="fas fa-user-plus me-2"></i>Add User
                                </a>
                                <a class="dropdown-item" href="<?php echo url('admin/users/pending.php'); ?>">
                                    <i class="fas fa-user-clock me-2"></i>Pending Approvals
                                </a>
                                <a class="dropdown-item" href="<?php echo url('admin/users/suspended.php'); ?>">
                                    <i class="fas fa-user-slash me-2"></i>Suspended Users
                                </a>
                            </div>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/accounts') !== false) ? 'active' : ''; ?>" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-university me-2"></i>
                                Account Management
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="<?php echo url('admin/accounts/'); ?>">
                                    <i class="fas fa-wallet me-2"></i>All Accounts
                                </a>
                                <a class="dropdown-item" href="<?php echo url('admin/accounts/add.php'); ?>">
                                    <i class="fas fa-plus me-2"></i>Create Account
                                </a>
                                <a class="dropdown-item" href="<?php echo url('admin/accounts/frozen.php'); ?>">
                                    <i class="fas fa-snowflake me-2"></i>Frozen Accounts
                                </a>
                            </div>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/transactions') !== false) ? 'active' : ''; ?>" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-exchange-alt me-2"></i>
                                Transactions
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="<?php echo url('admin/transactions/'); ?>">
                                    <i class="fas fa-list me-2"></i>All Transactions
                                </a>
                                <a class="dropdown-item" href="<?php echo url('admin/transactions/add.php'); ?>">
                                    <i class="fas fa-plus me-2"></i>Manual Transaction
                                </a>
                                <a class="dropdown-item" href="<?php echo url('admin/transactions/pending.php'); ?>">
                                    <i class="fas fa-clock me-2"></i>Pending Transactions
                                </a>
                                <a class="dropdown-item" href="<?php echo url('admin/transactions/flagged.php'); ?>">
                                    <i class="fas fa-flag me-2"></i>Flagged Transactions
                                </a>
                            </div>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/reports') !== false) ? 'active' : ''; ?>" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-chart-bar me-2"></i>
                                Reports
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="<?php echo url('admin/reports/'); ?>">
                                    <i class="fas fa-file-alt me-2"></i>All Reports
                                </a>
                                <a class="dropdown-item" href="<?php echo url('admin/reports/financial.php'); ?>">
                                    <i class="fas fa-dollar-sign me-2"></i>Financial Reports
                                </a>
                                <a class="dropdown-item" href="<?php echo url('admin/reports/user-activity.php'); ?>">
                                    <i class="fas fa-user-friends me-2"></i>User Activity
                                </a>
                                <a class="dropdown-item" href="<?php echo url('admin/reports/security.php'); ?>">
                                    <i class="fas fa-shield-alt me-2"></i>Security Reports
                                </a>
                            </div>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle <?php echo (strpos($_SERVER['REQUEST_URI'], '/admin/settings') !== false || strpos($_SERVER['REQUEST_URI'], '/admin/logs') !== false || strpos($_SERVER['REQUEST_URI'], '/admin/backup') !== false) ? 'active' : ''; ?>" href="#" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-tools me-2"></i>
                                System
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="<?php echo url('admin/settings/'); ?>">
                                    <i class="fas fa-cogs me-2"></i>Settings
                                </a>
                                <a class="dropdown-item" href="<?php echo url('admin/logs/'); ?>">
                                    <i class="fas fa-clipboard-list me-2"></i>Activity Logs
                                </a>
                                <a class="dropdown-item" href="<?php echo url('admin/backup/'); ?>">
                                    <i class="fas fa-database me-2"></i>Backup & Restore
                                </a>
                                <a class="dropdown-item" href="<?php echo url('admin/virtual-cards/'); ?>">
                                    <i class="fas fa-credit-card me-2"></i>Virtual Cards
                                </a>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </header>
    
    <!-- Flash Messages -->
    <?php if (hasFlashMessage('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars(getFlashMessage('success')); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (hasFlashMessage('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>
            <?php echo htmlspecialchars(getFlashMessage('error')); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <?php if (hasFlashMessage('warning')): ?>
        <div class="alert alert-warning alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars(getFlashMessage('warning')); ?>
            <button type="button" the="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>
    
    <!-- Admin Alert Banner -->
    <div class="alert admin-alert border-0 rounded-0 mb-0" role="alert">
        <div class="container-xl">
            <div class="d-flex align-items-center">
                <i class="fas fa-shield-alt me-2"></i>
                <strong>Administrator Mode:</strong> You are currently logged in as an administrator with full system access.
                <a href="<?php echo url('dashboard.php'); ?>" class="btn btn-sm btn-outline-light ms-auto">
                    <i class="fas fa-eye me-1"></i>
                    Switch to User View
                </a>
            </div>
        </div>
    </div>
    
    <!-- Main Content Container -->
    <div class="page-wrapper">
        <div class="page-body">
            <div class="container-xl">
                
    <!-- Tabler Core -->
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.3.2/dist/js/tabler.min.js"></script>
    
    <!-- Admin specific scripts -->
    <script src="<?php echo asset('admin/admin.js'); ?>"></script>

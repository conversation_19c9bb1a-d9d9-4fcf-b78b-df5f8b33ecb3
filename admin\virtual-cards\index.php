<?php
require_once '../../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    redirect('../login.php');
}

$page_title = 'Virtual Cards Management';

// Define page actions
$page_actions = [
    [
        'url' => '../users.php',
        'label' => 'View Users',
        'icon' => 'fas fa-users'
    ]
];

$errors = [];
$success = '';

// Handle card creation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create_card') {
        $user_id = intval($_POST['user_id'] ?? 0);
        $card_type = sanitizeInput($_POST['card_type'] ?? 'visa');
        $spending_limit = floatval($_POST['spending_limit'] ?? 1000);
        $initial_balance = floatval($_POST['initial_balance'] ?? 0);
        
        if ($user_id <= 0) {
            $errors[] = 'Please select a valid user.';
        }
        
        if ($spending_limit <= 0) {
            $errors[] = 'Spending limit must be greater than zero.';
        }
        
        if ($initial_balance < 0) {
            $errors[] = 'Initial balance cannot be negative.';
        }
        
        if (empty($errors)) {
            try {
                $db = getDB();
                $db->beginTransaction();
                
                // Get user info
                $user_result = $db->query("SELECT first_name, last_name FROM accounts WHERE id = ? AND is_admin = 0", [$user_id]);
                
                if ($user_result->num_rows === 0) {
                    $errors[] = 'User not found.';
                } else {
                    $user = $user_result->fetch_assoc();
                    $card_holder_name = strtoupper($user['first_name'] . ' ' . $user['last_name']);
                    
                    // Generate card details
                    $card_number = generateCardNumber($card_type);
                    $expiry_month = date('n') + 36; // 3 years from now
                    $expiry_year = date('Y');
                    
                    if ($expiry_month > 12) {
                        $expiry_year += floor($expiry_month / 12);
                        $expiry_month = $expiry_month % 12;
                        if ($expiry_month === 0) {
                            $expiry_month = 12;
                            $expiry_year--;
                        }
                    }
                    
                    $cvv = str_pad(mt_rand(100, 999), 3, '0', STR_PAD_LEFT);
                    
                    // Create virtual card
                    $card_sql = "INSERT INTO virtual_cards (
                                   user_id, card_number, card_holder_name, expiry_month, expiry_year,
                                   cvv, card_type, spending_limit, current_balance, linked_account_id
                                 ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    
                    $card_id = $db->insert($card_sql, [
                        $user_id, $card_number, $card_holder_name, $expiry_month, $expiry_year,
                        $cvv, $card_type, $spending_limit, $initial_balance, $user_id
                    ]);
                    
                    if ($card_id) {
                        // Log activity
                        logActivity($_SESSION['user_id'], 'Admin created virtual card', 'virtual_cards', $card_id);
                        
                        $db->commit();
                        $success = "Virtual card created successfully! Card Number: " . formatCardNumber($card_number);
                    } else {
                        $errors[] = 'Failed to create virtual card.';
                    }
                }
                
            } catch (Exception $e) {
                $db->rollback();
                error_log("Virtual card creation error: " . $e->getMessage());
                $errors[] = 'Failed to create virtual card. Please try again.';
            }
        }
    }
}

// Get users for dropdown
try {
    $db = getDB();

    // Reset result pointer for users
    $users_result = $db->query("SELECT id, first_name, last_name, username, account_number FROM accounts WHERE is_admin = 0 ORDER BY first_name, last_name");

    // Get virtual cards
    $cards_result = $db->query("
        SELECT vc.*, a.first_name, a.last_name, a.username
        FROM virtual_cards vc
        LEFT JOIN accounts a ON vc.user_id = a.id
        ORDER BY vc.created_at DESC
    ");

} catch (Exception $e) {
    error_log("Virtual cards fetch error: " . $e->getMessage());
    $users_result = null;
    $cards_result = null;
}

// Helper function to generate card number
function generateCardNumber($card_type) {
    $prefixes = [
        'visa' => '4',
        'mastercard' => '5',
        'amex' => '37'
    ];
    
    $prefix = $prefixes[$card_type] ?? '4';
    $number = $prefix . str_pad(mt_rand(***************, ***************), 15, '0', STR_PAD_LEFT);
    
    return substr($number, 0, 16);
}

// Helper function to format card number
function formatCardNumber($number) {
    return chunk_split($number, 4, ' ');
}

include '../includes/admin-header.php';
?>

<link rel="stylesheet" href="style.css">

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="../index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Virtual Cards</li>
    </ol>
</nav>
<?php if (!empty($errors)): ?>
<div class="alert alert-danger" role="alert">
                <div class="d-flex">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <circle cx="12" cy="12" r="9"/>
                            <line x1="15" y1="9" x2="9" y2="15"/>
                            <line x1="9" y1="9" x2="15" y2="15"/>
                        </svg>
                    </div>
                    <div>
                        <h4 class="alert-title">Error!</h4>
                        <ul class="mb-0">
                            <?php foreach ($errors as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
</div>
<?php endif; ?>

<?php if (!empty($success)): ?>
<div class="alert alert-success" role="alert">
                <div class="d-flex">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M5 12l5 5l10 -10"/>
                        </svg>
                    </div>
                    <div>
                        <h4 class="alert-title">Success!</h4>
                        <div class="text-muted"><?php echo htmlspecialchars($success); ?></div>
                    </div>
                </div>
</div>
<?php endif; ?>

<div class="row row-cards">
    <!-- Create Virtual Card -->
    <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <rect x="1" y="5" width="22" height="14" rx="7"/>
                                    <path d="M8 11l0 2"/>
                                    <path d="M12 11l0 2"/>
                                    <path d="M16 11l0 2"/>
                                </svg>
                                Create Virtual Card
                            </h3>
                        </div>
                        <div class="card-body">
                            <form method="POST" action="">
                                <input type="hidden" name="action" value="create_card">
                                
                                <div class="mb-3">
                                    <label class="form-label required">Select User</label>
                                    <select name="user_id" class="form-select" required>
                                        <option value="">Choose a user...</option>
                                        <?php if ($users_result): ?>
                                        <?php while ($user = $users_result->fetch_assoc()): ?>
                                        <option value="<?php echo $user['id']; ?>">
                                            <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?> 
                                            (@<?php echo htmlspecialchars($user['username']); ?>)
                                        </option>
                                        <?php endwhile; ?>
                                        <?php endif; ?>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label required">Card Type</label>
                                    <div class="form-selectgroup">
                                        <label class="form-selectgroup-item">
                                            <input type="radio" name="card_type" value="visa" class="form-selectgroup-input" checked>
                                            <span class="form-selectgroup-label">
                                                <div class="card-type-logo visa">VISA</div>
                                            </span>
                                        </label>
                                        <label class="form-selectgroup-item">
                                            <input type="radio" name="card_type" value="mastercard" class="form-selectgroup-input">
                                            <span class="form-selectgroup-label">
                                                <div class="card-type-logo mastercard">MC</div>
                                            </span>
                                        </label>
                                        <label class="form-selectgroup-item">
                                            <input type="radio" name="card_type" value="amex" class="form-selectgroup-input">
                                            <span class="form-selectgroup-label">
                                                <div class="card-type-logo amex">AMEX</div>
                                            </span>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label required">Spending Limit</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" name="spending_limit" class="form-control" step="0.01" min="1" value="1000" required>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Initial Balance</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" name="initial_balance" class="form-control" step="0.01" min="0" value="0">
                                    </div>
                                </div>
                                
                                <div class="form-footer">
                                    <button type="submit" class="btn btn-primary">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <line x1="12" y1="5" x2="12" y2="19"/>
                                            <line x1="5" y1="12" x2="19" y2="12"/>
                                        </svg>
                                        Create Virtual Card
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Virtual Cards List -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Virtual Cards</h3>
                        </div>
                        <div class="card-body p-0">
                            <?php if ($cards_result && $cards_result->num_rows > 0): ?>
                            <div class="row g-3 p-3">
                                <?php while ($card = $cards_result->fetch_assoc()): ?>
                                <div class="col-md-6">
                                    <div class="virtual-card <?php echo $card['card_type']; ?> <?php echo $card['status']; ?>">
                                        <div class="card-header-info">
                                            <div class="card-type-badge"><?php echo strtoupper($card['card_type']); ?></div>
                                            <div class="card-status-badge status-<?php echo $card['status']; ?>">
                                                <?php echo ucfirst($card['status']); ?>
                                            </div>
                                        </div>
                                        
                                        <div class="card-number">
                                            <?php echo formatCardNumber($card['card_number']); ?>
                                        </div>
                                        
                                        <div class="card-details">
                                            <div class="card-holder">
                                                <div class="label">CARD HOLDER</div>
                                                <div class="value"><?php echo htmlspecialchars($card['card_holder_name']); ?></div>
                                            </div>
                                            <div class="card-expiry">
                                                <div class="label">EXPIRES</div>
                                                <div class="value"><?php echo sprintf('%02d/%02d', $card['expiry_month'], $card['expiry_year']); ?></div>
                                            </div>
                                            <div class="card-cvv">
                                                <div class="label">CVV</div>
                                                <div class="value">***</div>
                                            </div>
                                        </div>
                                        
                                        <div class="card-balance">
                                            <div class="balance-info">
                                                <div class="current-balance">
                                                    <span class="label">Balance:</span>
                                                    <span class="amount"><?php echo formatCurrency($card['current_balance']); ?></span>
                                                </div>
                                                <div class="spending-limit">
                                                    <span class="label">Limit:</span>
                                                    <span class="amount"><?php echo formatCurrency($card['spending_limit']); ?></span>
                                                </div>
                                            </div>
                                            <div class="progress mt-2">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: <?php echo min(($card['current_balance'] / $card['spending_limit']) * 100, 100); ?>%">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="card-actions">
                                            <button class="btn btn-sm btn-outline-primary" onclick="topUpCard(<?php echo $card['id']; ?>)">
                                                Top Up
                                            </button>
                                            <button class="btn btn-sm btn-outline-secondary" onclick="viewCardDetails(<?php echo $card['id']; ?>)">
                                                Details
                                            </button>
                                            <?php if ($card['status'] === 'active'): ?>
                                            <button class="btn btn-sm btn-outline-warning" onclick="blockCard(<?php echo $card['id']; ?>)">
                                                Block
                                            </button>
                                            <?php else: ?>
                                            <button class="btn btn-sm btn-outline-success" onclick="activateCard(<?php echo $card['id']; ?>)">
                                                Activate
                                            </button>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <div class="card-user-info">
                                            <small class="text-muted">
                                                Issued to: <?php echo htmlspecialchars($card['first_name'] . ' ' . $card['last_name']); ?> 
                                                (@<?php echo htmlspecialchars($card['username']); ?>)
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <?php endwhile; ?>
                            </div>
                            <?php else: ?>
                            <div class="empty">
                                <div class="empty-img">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik02NCA2NlY1NCIgc3Ryb2tlPSIjREFEREUyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cGF0aCBkPSJNNjQgNzhWNzYiIHN0cm9rZT0iI0RBRERFRSI+PHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" alt="" height="128" width="128">
                                </div>
                                <p class="empty-title">No virtual cards yet</p>
                                <p class="empty-subtitle text-muted">
                                    Create virtual cards for users to manage their spending.
                                </p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="script.js"></script>

<?php include '../includes/admin-footer.php'; ?>

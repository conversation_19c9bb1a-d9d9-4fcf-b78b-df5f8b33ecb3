-- Online Banking System Database Schema
-- Compatible with MySQL/MariaDB

CREATE DATABASE IF NOT EXISTS online_banking;
USE online_banking;

-- Users/Accounts table
CREATE TABLE accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    account_number VARCHAR(20) UNIQUE NOT NULL,
    username VA<PERSON>HA<PERSON>(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    date_of_birth DATE,
    occupation VARCHAR(100),
    marital_status ENUM('single', 'married', 'divorced', 'widowed') DEFAULT 'single',
    gender ENUM('male', 'female', 'other') DEFAULT 'male',
    currency VARCHAR(3) DEFAULT 'USD',
    account_type ENUM('savings', 'checking', 'business') DEFAULT 'savings',
    balance DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('active', 'suspended', 'closed') DEFAULT 'active',
    pin VARCHAR(255),
    is_admin BOOLEAN DEFAULT FALSE,
    kyc_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- Transfers/Transactions table
CREATE TABLE transfers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    transaction_id VARCHAR(50) UNIQUE NOT NULL,
    sender_id INT,
    recipient_id INT,
    recipient_account VARCHAR(20),
    recipient_name VARCHAR(100),
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    transfer_type ENUM('local', 'international', 'bitcoin') DEFAULT 'local',
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    description TEXT,
    fee DECIMAL(10,2) DEFAULT 0.00,
    exchange_rate DECIMAL(10,4) DEFAULT 1.0000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (sender_id) REFERENCES accounts(id) ON DELETE SET NULL,
    FOREIGN KEY (recipient_id) REFERENCES accounts(id) ON DELETE SET NULL
);

-- Beneficiaries table
CREATE TABLE beneficiaries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    account_number VARCHAR(20) NOT NULL,
    bank_name VARCHAR(100),
    bank_code VARCHAR(20),
    country VARCHAR(50) DEFAULT 'USA',
    currency VARCHAR(3) DEFAULT 'USD',
    is_favorite BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE
);

-- Support tickets table
CREATE TABLE tickets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    ticket_number VARCHAR(20) UNIQUE NOT NULL,
    user_id INT NOT NULL,
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    status ENUM('open', 'in_progress', 'resolved', 'closed') DEFAULT 'open',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    assigned_to INT NULL,
    attachment VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES accounts(id) ON DELETE SET NULL
);

-- Audit logs table
CREATE TABLE audit_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50),
    record_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE SET NULL
);

-- System settings table
CREATE TABLE system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES accounts(id) ON DELETE SET NULL
);

-- Login attempts table (for security)
CREATE TABLE login_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50),
    ip_address VARCHAR(45),
    success BOOLEAN DEFAULT FALSE,
    attempted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default admin user (password: admin123)
INSERT INTO accounts (
    account_number, username, password, email, first_name, last_name, 
    account_type, balance, is_admin, status, kyc_status
) VALUES (
    '1********1', 
    'admin', 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
    '<EMAIL>', 
    'System', 
    'Administrator', 
    'business', 
    0.00, 
    TRUE, 
    'active', 
    'verified'
);

-- Insert sample user (password: user123)
INSERT INTO accounts (
    account_number, username, password, email, first_name, last_name, 
    account_type, balance, status, kyc_status
) VALUES (
    '1********2', 
    'john_doe', 
    '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
    '<EMAIL>', 
    'John', 
    'Doe', 
    'savings', 
    5000.00, 
    'active', 
    'verified'
);

-- Virtual cards table
CREATE TABLE virtual_cards (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    card_number VARCHAR(19) NOT NULL,
    card_holder_name VARCHAR(100) NOT NULL,
    expiry_month INT NOT NULL,
    expiry_year INT NOT NULL,
    cvv VARCHAR(4) NOT NULL,
    card_type ENUM('visa', 'mastercard', 'amex') DEFAULT 'visa',
    status ENUM('active', 'blocked', 'expired') DEFAULT 'active',
    spending_limit DECIMAL(15,2) DEFAULT 1000.00,
    current_balance DECIMAL(15,2) DEFAULT 0.00,
    linked_account_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (linked_account_id) REFERENCES accounts(id) ON DELETE SET NULL,
    UNIQUE KEY unique_card_number (card_number),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- Account transactions for credit/debit operations
CREATE TABLE account_transactions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_id INT NOT NULL,
    transaction_type ENUM('credit', 'debit') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    description TEXT,
    reference_number VARCHAR(50),
    category ENUM('deposit', 'withdrawal', 'transfer', 'fee', 'interest', 'adjustment', 'virtual_card', 'crypto') DEFAULT 'adjustment',
    status ENUM('pending', 'completed', 'failed', 'cancelled') DEFAULT 'completed',
    processed_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES accounts(id) ON DELETE SET NULL,
    INDEX idx_account_id (account_id),
    INDEX idx_transaction_type (transaction_type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- Cryptocurrency accounts
CREATE TABLE crypto_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    crypto_type ENUM('bitcoin', 'ethereum', 'litecoin', 'dogecoin', 'usdt') NOT NULL,
    wallet_address VARCHAR(100),
    balance DECIMAL(20,8) DEFAULT 0.********,
    usd_equivalent DECIMAL(15,2) DEFAULT 0.00,
    status ENUM('active', 'suspended', 'closed') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES accounts(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_crypto_type (crypto_type),
    INDEX idx_status (status)
);

-- Currency exchange rates
CREATE TABLE exchange_rates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    from_currency VARCHAR(3) NOT NULL,
    to_currency VARCHAR(3) NOT NULL,
    rate DECIMAL(10,6) NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_currency_pair (from_currency, to_currency),
    INDEX idx_from_currency (from_currency),
    INDEX idx_to_currency (to_currency)
);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES
('bank_name', 'SecureBank Online', 'Bank name displayed in the application'),
('currency', 'USD', 'Default currency for the system'),
('min_transfer_amount', '1.00', 'Minimum transfer amount'),
('max_transfer_amount', '10000.00', 'Maximum transfer amount per transaction'),
('daily_transfer_limit', '25000.00', 'Daily transfer limit per user'),
('maintenance_mode', 'false', 'Enable/disable maintenance mode'),
('registration_enabled', 'true', 'Enable/disable new user registration'),
('theme', 'light', 'Application theme (light/dark)'),
('color_scheme', 'blue', 'Application color scheme'),
('sidebar_collapsed', 'false', 'Default sidebar state'),
('header_fixed', 'false', 'Fixed header setting'),
('animations_enabled', 'true', 'Enable/disable animations'),
('supported_currencies', 'USD,EUR,GBP,CAD,AUD,JPY', 'Comma-separated list of supported currencies'),
('crypto_enabled', 'true', 'Enable cryptocurrency features'),
('virtual_cards_enabled', 'true', 'Enable virtual card features'),
('default_card_limit', '1000.00', 'Default spending limit for new virtual cards');

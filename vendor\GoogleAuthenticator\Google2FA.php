<?php
/**
 * Simple Google Authenticator Implementation
 * For Online Banking System
 */

class Google2FA
{
    private $secretLength = 16;
    private $window = 1; // Allow 1 window before and after current time
    
    /**
     * Generate a random secret key
     */
    public function generateSecretKey($length = null)
    {
        $length = $length ?: $this->secretLength;
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $secret = '';
        
        for ($i = 0; $i < $length; $i++) {
            $secret .= $chars[random_int(0, strlen($chars) - 1)];
        }
        
        return $secret;
    }
    
    /**
     * Get QR Code URL for Google Authenticator
     */
    public function getQRCodeUrl($company, $holder, $secret)
    {
        $label = urlencode($company . ':' . $holder);
        $issuer = urlencode($company);
        
        $otpauth = "otpauth://totp/{$label}?secret={$secret}&issuer={$issuer}";
        
        return "https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=" . urlencode($otpauth);
    }
    
    /**
     * Verify TOTP code
     */
    public function verifyKey($secret, $code, $window = null)
    {
        $window = $window ?: $this->window;
        $currentTime = time();
        
        // Check current time and windows before/after
        for ($i = -$window; $i <= $window; $i++) {
            $time = $currentTime + ($i * 30); // 30 second intervals
            $calculatedCode = $this->calculateCode($secret, $time);
            
            if ($calculatedCode === $code) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Calculate TOTP code for given time
     */
    private function calculateCode($secret, $time = null)
    {
        $time = $time ?: time();
        $timeSlice = intval($time / 30);
        
        $secretKey = $this->base32Decode($secret);
        $timeBytes = pack('N*', 0) . pack('N*', $timeSlice);
        
        $hash = hash_hmac('sha1', $timeBytes, $secretKey, true);
        $offset = ord($hash[19]) & 0xf;
        
        $code = (
            ((ord($hash[$offset + 0]) & 0x7f) << 24) |
            ((ord($hash[$offset + 1]) & 0xff) << 16) |
            ((ord($hash[$offset + 2]) & 0xff) << 8) |
            (ord($hash[$offset + 3]) & 0xff)
        ) % 1000000;
        
        return str_pad($code, 6, '0', STR_PAD_LEFT);
    }
    
    /**
     * Base32 decode
     */
    private function base32Decode($secret)
    {
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $secret = strtoupper($secret);
        $decoded = '';
        
        for ($i = 0; $i < strlen($secret); $i += 8) {
            $chunk = substr($secret, $i, 8);
            $chunk = str_pad($chunk, 8, '=');
            
            $binary = '';
            for ($j = 0; $j < 8; $j++) {
                if ($chunk[$j] !== '=') {
                    $binary .= str_pad(decbin(strpos($chars, $chunk[$j])), 5, '0', STR_PAD_LEFT);
                }
            }
            
            for ($j = 0; $j < strlen($binary); $j += 8) {
                $byte = substr($binary, $j, 8);
                if (strlen($byte) === 8) {
                    $decoded .= chr(bindec($byte));
                }
            }
        }
        
        return $decoded;
    }
    
    /**
     * Get current TOTP code (for testing)
     */
    public function getCurrentOtp($secret)
    {
        return $this->calculateCode($secret);
    }
}

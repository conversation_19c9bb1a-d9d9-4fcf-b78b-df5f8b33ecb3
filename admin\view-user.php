<?php
require_once '../config/config.php';
requireAdmin();

// Get user ID from URL
$user_id = intval($_GET['id'] ?? 0);

if ($user_id <= 0) {
    header('Location: users.php');
    exit();
}

$page_title = 'User Details';
$user = null;
$user_stats = [];
$recent_transactions = [];
$virtual_cards = [];
$crypto_accounts = [];
$beneficiaries = [];
$support_tickets = [];
$login_attempts = [];
$otp_history = [];
$user_documents = [];
$kyc_application = null;
$document_history = [];

try {
    $db = getDB();

    // Get user details
    $user_query = "SELECT * FROM accounts WHERE id = ? AND is_admin = 0";
    $user_result = $db->query($user_query, [$user_id]);

    if ($user_result->num_rows === 0) {
        header('Location: users.php');
        exit();
    }

    $user = $user_result->fetch_assoc();

    // Get user statistics
    $stats_query = "SELECT
        COUNT(CASE WHEN at.transaction_type = 'credit' THEN 1 END) as total_credits,
        COUNT(CASE WHEN at.transaction_type = 'debit' THEN 1 END) as total_debits,
        SUM(CASE WHEN at.transaction_type = 'credit' THEN at.amount ELSE 0 END) as total_credited,
        SUM(CASE WHEN at.transaction_type = 'debit' THEN at.amount ELSE 0 END) as total_debited,
        COUNT(CASE WHEN at.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as monthly_transactions
        FROM account_transactions at
        WHERE at.account_id = ?";
    $stats_result = $db->query($stats_query, [$user_id]);
    $user_stats = $stats_result->fetch_assoc();

    // Get recent transactions (last 10) - combining all transaction types
    $transactions_query = "SELECT 'account' as source, id, transaction_type, amount, currency, description,
                          reference_number, status, created_at, NULL as card_number, NULL as crypto_type
                          FROM account_transactions
                          WHERE account_id = ?
                          ORDER BY created_at DESC
                          LIMIT 10";
    $transactions_result = $db->query($transactions_query, [$user_id]);
    while ($transaction = $transactions_result->fetch_assoc()) {
        $recent_transactions[] = $transaction;
    }

    // Get virtual card transactions (with error handling)
    $virtual_card_transactions = [];
    try {
        $vcard_trans_query = "SELECT vct.*, vc.card_number
                             FROM virtual_card_transactions vct
                             LEFT JOIN virtual_cards vc ON vct.card_id = vc.card_id
                             WHERE vct.account_id = ?
                             ORDER BY vct.created_at DESC
                             LIMIT 10";
        $vcard_trans_result = $db->query($vcard_trans_query, [$user_id]);
        while ($transaction = $vcard_trans_result->fetch_assoc()) {
            $transaction['source'] = 'virtual_card';
            $virtual_card_transactions[] = $transaction;
        }
    } catch (Exception $e) {
        error_log("Virtual card transactions query error: " . $e->getMessage());
    }

    // Get crypto transactions (with error handling)
    $crypto_transactions = [];
    try {
        $crypto_trans_query = "SELECT ct.*, cw.cryptocurrency as crypto_type
                              FROM crypto_transactions ct
                              LEFT JOIN crypto_wallets cw ON ct.wallet_id = cw.id
                              WHERE ct.account_id = ?
                              ORDER BY ct.created_at DESC
                              LIMIT 10";
        $crypto_trans_result = $db->query($crypto_trans_query, [$user_id]);
        while ($transaction = $crypto_trans_result->fetch_assoc()) {
            $transaction['source'] = 'crypto';
            $crypto_transactions[] = $transaction;
        }
    } catch (Exception $e) {
        error_log("Crypto transactions query error: " . $e->getMessage());
    }

    // Combine all transactions and sort by date
    $all_transactions = array_merge($recent_transactions, $virtual_card_transactions, $crypto_transactions);
    usort($all_transactions, function($a, $b) {
        return strtotime($b['created_at']) - strtotime($a['created_at']);
    });
    $recent_transactions = array_slice($all_transactions, 0, 10);

    // Get virtual cards (with error handling) - using account_id column
    try {
        $cards_query = "SELECT * FROM virtual_cards WHERE account_id = ? ORDER BY created_at DESC";
        $cards_result = $db->query($cards_query, [$user_id]);
        while ($card = $cards_result->fetch_assoc()) {
            $virtual_cards[] = $card;
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
        error_log("Virtual cards query error: " . $e->getMessage());
    }

    // Get crypto accounts (with error handling) - using crypto_wallets table
    try {
        $crypto_query = "SELECT * FROM crypto_wallets WHERE account_id = ? ORDER BY cryptocurrency";
        $crypto_result = $db->query($crypto_query, [$user_id]);
        while ($crypto = $crypto_result->fetch_assoc()) {
            $crypto_accounts[] = $crypto;
        }
    } catch (Exception $e) {
        // Try alternative table name
        try {
            $crypto_query = "SELECT * FROM crypto_accounts WHERE user_id = ? ORDER BY crypto_type";
            $crypto_result = $db->query($crypto_query, [$user_id]);
            while ($crypto = $crypto_result->fetch_assoc()) {
                $crypto_accounts[] = $crypto;
            }
        } catch (Exception $e2) {
            // Neither table exists, continue without error
            error_log("Crypto accounts query error: " . $e->getMessage() . " | " . $e2->getMessage());
        }
    }

    // Get beneficiaries (with error handling)
    try {
        $beneficiaries_query = "SELECT * FROM beneficiaries WHERE user_id = ? ORDER BY name";
        $beneficiaries_result = $db->query($beneficiaries_query, [$user_id]);
        while ($beneficiary = $beneficiaries_result->fetch_assoc()) {
            $beneficiaries[] = $beneficiary;
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
    }

    // Get support tickets (with error handling)
    try {
        $tickets_query = "SELECT * FROM tickets WHERE user_id = ? ORDER BY created_at DESC LIMIT 5";
        $tickets_result = $db->query($tickets_query, [$user_id]);
        while ($ticket = $tickets_result->fetch_assoc()) {
            $support_tickets[] = $ticket;
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
    }

    // Get recent login attempts (with error handling)
    try {
        $login_query = "SELECT * FROM login_attempts WHERE username = ? ORDER BY attempted_at DESC LIMIT 10";
        $login_result = $db->query($login_query, [$user['username']]);
        while ($attempt = $login_result->fetch_assoc()) {
            $login_attempts[] = $attempt;
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
    }

    // Get OTP history (with error handling)
    try {
        $otp_query = "SELECT * FROM user_otps WHERE user_id = ? ORDER BY created_at DESC LIMIT 10";
        $otp_result = $db->query($otp_query, [$user_id]);
        while ($otp = $otp_result->fetch_assoc()) {
            $otp_history[] = $otp;
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
    }

    // Get user documents (with error handling)
    try {
        $docs_query = "SELECT * FROM user_documents WHERE user_id = ? ORDER BY created_at DESC";
        $docs_result = $db->query($docs_query, [$user_id]);
        while ($doc = $docs_result->fetch_assoc()) {
            $user_documents[] = $doc;
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
        error_log("User documents query error: " . $e->getMessage());
    }

    // Get KYC application (with error handling)
    try {
        $kyc_query = "SELECT * FROM kyc_applications WHERE user_id = ? ORDER BY created_at DESC LIMIT 1";
        $kyc_result = $db->query($kyc_query, [$user_id]);
        if ($kyc_result->num_rows > 0) {
            $kyc_application = $kyc_result->fetch_assoc();
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
        error_log("KYC application query error: " . $e->getMessage());
    }

    // Get document verification history (with error handling)
    try {
        $doc_history_query = "SELECT dvh.*, ud.document_name, ud.document_type, a.first_name, a.last_name
                             FROM document_verification_history dvh
                             LEFT JOIN user_documents ud ON dvh.document_id = ud.id
                             LEFT JOIN accounts a ON dvh.performed_by = a.id
                             WHERE ud.user_id = ?
                             ORDER BY dvh.created_at DESC
                             LIMIT 20";
        $doc_history_result = $db->query($doc_history_query, [$user_id]);
        while ($history = $doc_history_result->fetch_assoc()) {
            $document_history[] = $history;
        }
    } catch (Exception $e) {
        // Table might not exist, continue without error
        error_log("Document history query error: " . $e->getMessage());
    }

} catch (Exception $e) {
    error_log("Error loading user details: " . $e->getMessage());
    header('Location: users.php');
    exit();
}

include 'includes/admin-header.php';

// Helper function for file size formatting
function formatFileSize($bytes) {
    if ($bytes >= **********) {
        return number_format($bytes / **********, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item"><a href="users.php">Users</a></li>
        <li class="breadcrumb-item active" aria-current="page">User Details</li>
    </ol>
</nav>

<!-- Page Header -->
<div class="page-header d-print-none">
    <div class="container-xl">
        <div class="row g-2 align-items-center">
            <div class="col">
                <div class="d-flex align-items-center">
                    <span class="avatar avatar-lg me-3" style="background: linear-gradient(135deg, #206bc4, #1a5490);">
                        <?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?>
                    </span>
                    <div>
                        <h2 class="page-title mb-1">
                            <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                        </h2>
                        <div class="text-muted">
                            <span class="me-3">@<?php echo htmlspecialchars($user['username']); ?></span>
                            <span class="me-3"><?php echo htmlspecialchars($user['account_number']); ?></span>
                            <span class="badge bg-<?php echo $user['status'] === 'active' ? 'green' : ($user['status'] === 'suspended' ? 'red' : 'gray'); ?>-lt">
                                <?php echo ucfirst($user['status']); ?>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-auto ms-auto d-print-none">
                <div class="btn-list">
                    <a href="edit-user.php?id=<?php echo $user['id']; ?>" class="btn btn-primary">
                        <i class="fas fa-edit me-2"></i>
                        Edit User
                    </a>
                    <a href="users.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Users
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Overview Cards -->
<div class="row row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                            <i class="fas fa-wallet"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium">
                            <?php echo formatCurrency($user['balance'], $user['currency']); ?>
                        </div>
                        <div class="text-muted">Current Balance</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-success text-white avatar">
                            <i class="fas fa-arrow-up"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium">
                            <?php echo formatCurrency($user_stats['total_credited'] ?? 0, $user['currency']); ?>
                        </div>
                        <div class="text-muted">Total Credited</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-danger text-white avatar">
                            <i class="fas fa-arrow-down"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium">
                            <?php echo formatCurrency($user_stats['total_debited'] ?? 0, $user['currency']); ?>
                        </div>
                        <div class="text-muted">Total Debited</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-sm-6 col-lg-3">
        <div class="card card-sm">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-auto">
                        <span class="bg-info text-white avatar">
                            <i class="fas fa-chart-line"></i>
                        </span>
                    </div>
                    <div class="col">
                        <div class="font-weight-medium">
                            <?php echo number_format($user_stats['monthly_transactions'] ?? 0); ?>
                        </div>
                        <div class="text-muted">Monthly Activity</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row row-cards">
    <!-- Personal Information -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-user me-2"></i>
                    Personal Information
                </h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-5">Full Name:</dt>
                            <dd class="col-7"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></dd>

                            <dt class="col-5">Email:</dt>
                            <dd class="col-7"><?php echo htmlspecialchars($user['email']); ?></dd>

                            <dt class="col-5">Phone:</dt>
                            <dd class="col-7"><?php echo htmlspecialchars($user['phone'] ?? 'Not provided'); ?></dd>

                            <dt class="col-5">Date of Birth:</dt>
                            <dd class="col-7"><?php echo $user['date_of_birth'] ? formatDate($user['date_of_birth'], 'M j, Y') : 'Not provided'; ?></dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-5">Gender:</dt>
                            <dd class="col-7"><?php echo ucfirst($user['gender'] ?? 'Not specified'); ?></dd>

                            <dt class="col-5">Marital Status:</dt>
                            <dd class="col-7"><?php echo ucfirst($user['marital_status'] ?? 'Not specified'); ?></dd>

                            <dt class="col-5">Occupation:</dt>
                            <dd class="col-7"><?php echo htmlspecialchars($user['occupation'] ?? 'Not provided'); ?></dd>

                            <dt class="col-5">KYC Status:</dt>
                            <dd class="col-7">
                                <span class="badge bg-<?php echo $user['kyc_status'] === 'verified' ? 'green' : ($user['kyc_status'] === 'rejected' ? 'red' : 'yellow'); ?>-lt">
                                    <?php echo ucfirst($user['kyc_status']); ?>
                                </span>
                            </dd>
                        </dl>
                    </div>
                </div>

                <?php if (!empty($user['address'])): ?>
                <div class="mt-3">
                    <dl class="row">
                        <dt class="col-2">Address:</dt>
                        <dd class="col-10"><?php echo htmlspecialchars($user['address']); ?></dd>
                    </dl>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Account Information -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-university me-2"></i>
                    Account Information
                </h3>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-5">Account Number:</dt>
                    <dd class="col-7">
                        <span class="font-monospace"><?php echo htmlspecialchars($user['account_number']); ?></span>
                    </dd>

                    <dt class="col-5">Account Type:</dt>
                    <dd class="col-7"><?php echo ucfirst($user['account_type']); ?></dd>

                    <dt class="col-5">Currency:</dt>
                    <dd class="col-7"><?php echo $user['currency'] ?? 'USD'; ?></dd>

                    <dt class="col-5">Current Balance:</dt>
                    <dd class="col-7">
                        <strong class="text-primary"><?php echo formatCurrency($user['balance'], $user['currency']); ?></strong>
                    </dd>

                    <dt class="col-5">Account Status:</dt>
                    <dd class="col-7">
                        <span class="badge bg-<?php echo $user['status'] === 'active' ? 'green' : ($user['status'] === 'suspended' ? 'red' : 'gray'); ?>-lt">
                            <?php echo ucfirst($user['status']); ?>
                        </span>
                    </dd>

                    <dt class="col-5">Joined:</dt>
                    <dd class="col-7"><?php echo formatDate($user['created_at'], 'M j, Y g:i A'); ?></dd>

                    <dt class="col-5">Last Login:</dt>
                    <dd class="col-7">
                        <?php echo $user['last_login'] ? formatDate($user['last_login'], 'M j, Y g:i A') : 'Never'; ?>
                    </dd>

                    <dt class="col-5">Last Updated:</dt>
                    <dd class="col-7"><?php echo formatDate($user['updated_at'], 'M j, Y g:i A'); ?></dd>
                </dl>
            </div>
        </div>
    </div>
</div>

<!-- Security & OTP Management -->
<div class="row row-cards mt-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-shield-alt me-2"></i>
                    Security & Authentication
                </h3>
            </div>
            <div class="card-body">
                <!-- Current OTP Status -->
                <?php
                $current_otp = null;
                foreach ($otp_history as $otp) {
                    if (!$otp['used'] && strtotime($otp['expires_at']) > time()) {
                        $current_otp = $otp;
                        break;
                    }
                }
                ?>

                <?php if ($current_otp): ?>
                <div class="alert alert-info mb-3">
                    <div class="d-flex">
                        <div><i class="fas fa-key me-2"></i></div>
                        <div>
                            <h4 class="alert-title">Active OTP Code</h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Code:</strong> <span class="font-monospace badge bg-primary"><?php echo htmlspecialchars($current_otp['otp_code']); ?></span><br>
                                    <strong>Source:</strong> <?php echo ucfirst($current_otp['source'] ?? 'login'); ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>Expires:</strong> <?php echo formatDate($current_otp['expires_at'], 'M j, Y g:i A'); ?><br>
                                    <strong>Created:</strong> <?php echo formatDate($current_otp['created_at'], 'M j, Y g:i A'); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="alert alert-secondary mb-3">
                    <div class="d-flex">
                        <div><i class="fas fa-info-circle me-2"></i></div>
                        <div>
                            <h4 class="alert-title">No Active OTP</h4>
                            <p class="mb-0">This user does not currently have an active OTP code.</p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- OTP Management Actions -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card card-sm bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-key text-primary mb-2" style="font-size: 1.5rem;"></i>
                                <h4 class="card-title">Generate OTP</h4>
                                <p class="text-muted mb-3">Create a new OTP code for user support</p>
                                <button class="btn btn-primary btn-sm" onclick="generateOTPForUser(<?php echo $user['id']; ?>)">
                                    <i class="fas fa-plus me-1"></i>
                                    Generate New OTP
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card card-sm bg-light">
                            <div class="card-body text-center">
                                <i class="fas fa-history text-info mb-2" style="font-size: 1.5rem;"></i>
                                <h4 class="card-title">OTP History</h4>
                                <p class="text-muted mb-3">View recent OTP codes and usage</p>
                                <button class="btn btn-info btn-sm" onclick="showOTPHistory()">
                                    <i class="fas fa-list me-1"></i>
                                    View History
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    Login Activity
                </h3>
            </div>
            <div class="card-body">
                <?php if (!empty($login_attempts)): ?>
                <div class="list-group list-group-flush">
                    <?php foreach (array_slice($login_attempts, 0, 5) as $attempt): ?>
                    <div class="list-group-item d-flex justify-content-between align-items-start">
                        <div class="ms-2 me-auto">
                            <div class="fw-bold">
                                <?php echo $attempt['success'] ? 'Successful' : 'Failed'; ?>
                            </div>
                            <small class="text-muted">
                                <?php echo formatDate($attempt['attempted_at'], 'M j, g:i A'); ?>
                            </small>
                        </div>
                        <span class="badge bg-<?php echo $attempt['success'] ? 'success' : 'danger'; ?> rounded-pill">
                            <?php echo htmlspecialchars($attempt['ip_address']); ?>
                        </span>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center text-muted">
                    <i class="fas fa-info-circle mb-2"></i>
                    <p>No login attempts recorded</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Financial Information - Full Width Recent Transactions -->
<div class="row row-cards mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-exchange-alt me-2"></i>
                    Recent Transactions
                </h3>
                <div class="card-actions">
                    <a href="transactions.php?user_id=<?php echo $user['id']; ?>" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_transactions)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Description</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_transactions as $transaction): ?>
                            <tr>
                                <td>
                                    <div><?php echo formatDate($transaction['created_at'], 'M j, Y'); ?></div>
                                    <small class="text-muted"><?php echo formatDate($transaction['created_at'], 'g:i A'); ?></small>
                                </td>
                                <td>
                                    <?php
                                    $source = $transaction['source'] ?? 'account';
                                    $type = $transaction['transaction_type'];
                                    $badge_color = $type === 'credit' ? 'success' : 'danger';
                                    ?>
                                    <span class="badge bg-<?php echo $badge_color; ?>-lt">
                                        <?php echo ucfirst($type); ?>
                                    </span>
                                    <?php if ($source !== 'account'): ?>
                                    <br><small class="text-muted">
                                        <?php if ($source === 'virtual_card'): ?>
                                        <i class="fas fa-credit-card me-1"></i>Card
                                        <?php elseif ($source === 'crypto'): ?>
                                        <i class="fab fa-bitcoin me-1"></i>Crypto
                                        <?php endif; ?>
                                    </small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="fw-bold <?php echo $type === 'credit' ? 'text-success' : 'text-danger'; ?>">
                                        <?php
                                        $currency = $transaction['currency'] ?? 'USD';
                                        if ($source === 'crypto' && !empty($transaction['crypto_type'])) {
                                            echo ($type === 'credit' ? '+' : '-') . number_format($transaction['amount'], 8) . ' ' . strtoupper($transaction['crypto_type']);
                                        } else {
                                            echo ($type === 'credit' ? '+' : '-') . formatCurrency($transaction['amount'], $currency);
                                        }
                                        ?>
                                    </span>
                                    <?php if ($source === 'virtual_card' && !empty($transaction['card_number'])): ?>
                                    <br><small class="text-muted">**** <?php echo substr($transaction['card_number'], -4); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($transaction['description'] ?? ''); ?>">
                                        <?php echo htmlspecialchars($transaction['description'] ?? 'No description'); ?>
                                    </div>
                                    <?php if (!empty($transaction['reference_number'])): ?>
                                    <small class="text-muted font-monospace"><?php echo htmlspecialchars($transaction['reference_number']); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $status = $transaction['status'] ?? 'completed';
                                    $status_color = $status === 'completed' || $status === 'confirmed' ? 'success' : 'warning';
                                    ?>
                                    <span class="badge bg-<?php echo $status_color; ?>-lt">
                                        <?php echo ucfirst($status); ?>
                                    </span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center text-muted py-4">
                    <i class="fas fa-receipt mb-2" style="font-size: 2rem;"></i>
                    <p>No transactions found</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Virtual Cards & Crypto Accounts -->
<div class="row row-cards mt-4">
    <!-- Virtual Cards -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-credit-card me-2"></i>
                    Virtual Cards
                </h3>
            </div>
            <div class="card-body">
                <?php if (!empty($virtual_cards)): ?>
                <div class="list-group list-group-flush">
                    <?php foreach ($virtual_cards as $card): ?>
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="fw-bold">**** **** **** <?php echo substr($card['card_number'], -4); ?></div>
                                <small class="text-muted">
                                    <?php echo ucfirst($card['card_type'] ?? 'virtual'); ?> •
                                    <?php
                                    // Handle expiry date format
                                    if (!empty($card['expiry_date'])) {
                                        $expiry = date('m/y', strtotime($card['expiry_date']));
                                        echo $expiry;
                                    } else {
                                        echo 'No expiry';
                                    }
                                    ?>
                                </small>
                                <div class="text-muted small"><?php echo htmlspecialchars($card['card_holder_name']); ?></div>
                            </div>
                            <span class="badge bg-<?php echo $card['status'] === 'active' ? 'success' : 'danger'; ?>-lt">
                                <?php echo ucfirst($card['status']); ?>
                            </span>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                Balance: <?php echo formatCurrency($card['card_balance'] ?? 0, $card['currency'] ?? $user['currency']); ?>
                                <?php if (!empty($card['daily_limit'])): ?>
                                 / Daily Limit: <?php echo formatCurrency($card['daily_limit'], $card['currency'] ?? $user['currency']); ?>
                                <?php endif; ?>
                            </small>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center text-muted">
                    <i class="fas fa-credit-card mb-2"></i>
                    <p>No virtual cards</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Crypto Accounts -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fab fa-bitcoin me-2"></i>
                    Cryptocurrency Accounts
                </h3>
            </div>
            <div class="card-body">
                <?php if (!empty($crypto_accounts)): ?>
                <div class="list-group list-group-flush">
                    <?php foreach ($crypto_accounts as $crypto): ?>
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="fw-bold">
                                    <?php
                                    // Handle both table structures
                                    $crypto_type = $crypto['cryptocurrency'] ?? $crypto['crypto_type'] ?? 'Unknown';
                                    echo strtoupper($crypto_type);
                                    ?>
                                </div>
                                <small class="text-muted font-monospace">
                                    <?php echo htmlspecialchars($crypto['wallet_address'] ?? 'No address'); ?>
                                </small>
                                <?php if (!empty($crypto['wallet_name'])): ?>
                                <div class="text-muted small"><?php echo htmlspecialchars($crypto['wallet_name']); ?></div>
                                <?php endif; ?>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold">
                                    <?php
                                    $balance = $crypto['wallet_balance'] ?? $crypto['balance'] ?? 0;
                                    echo number_format($balance, 8);
                                    ?>
                                    <?php echo strtoupper($crypto_type); ?>
                                </div>
                                <?php if (!empty($crypto['usd_equivalent'])): ?>
                                <small class="text-muted">≈ $<?php echo number_format($crypto['usd_equivalent'], 2); ?></small>
                                <?php endif; ?>
                                <div>
                                    <span class="badge bg-<?php echo $crypto['status'] === 'active' ? 'success' : 'danger'; ?>-lt">
                                        <?php echo ucfirst($crypto['status']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center text-muted">
                    <i class="fab fa-bitcoin mb-2"></i>
                    <p>No cryptocurrency accounts</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Documents & KYC Management -->
<div class="row row-cards mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-file-alt me-2"></i>
                    Documents & KYC Management
                </h3>
                <div class="card-actions">
                    <button class="btn btn-sm btn-primary" onclick="uploadDocument()">
                        <i class="fas fa-upload me-1"></i>
                        Upload Document
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- KYC Status Overview -->
                <?php if ($kyc_application): ?>
                <div class="alert alert-<?php echo $kyc_application['application_status'] === 'approved' ? 'success' : ($kyc_application['application_status'] === 'rejected' ? 'danger' : 'warning'); ?> mb-4">
                    <div class="d-flex">
                        <div>
                            <i class="fas fa-<?php echo $kyc_application['application_status'] === 'approved' ? 'check-circle' : ($kyc_application['application_status'] === 'rejected' ? 'times-circle' : 'clock'); ?> me-2"></i>
                        </div>
                        <div>
                            <h4 class="alert-title">KYC Status: <?php echo ucfirst(str_replace('_', ' ', $kyc_application['application_status'])); ?></h4>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>Approval Level:</strong> <?php echo ucfirst($kyc_application['approval_level']); ?><br>
                                    <strong>Submitted:</strong> <?php echo $kyc_application['submitted_at'] ? formatDate($kyc_application['submitted_at'], 'M j, Y g:i A') : 'Not submitted'; ?>
                                </div>
                                <div class="col-md-6">
                                    <strong>Reviewed By:</strong> <?php echo $kyc_application['reviewed_by'] ? 'Admin' : 'Pending'; ?><br>
                                    <strong>Reviewed:</strong> <?php echo $kyc_application['reviewed_at'] ? formatDate($kyc_application['reviewed_at'], 'M j, Y g:i A') : 'Pending'; ?>
                                </div>
                            </div>
                            <?php if (!empty($kyc_application['notes'])): ?>
                            <div class="mt-2">
                                <strong>Notes:</strong> <?php echo htmlspecialchars($kyc_application['notes']); ?>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php else: ?>
                <div class="alert alert-info mb-4">
                    <div class="d-flex">
                        <div><i class="fas fa-info-circle me-2"></i></div>
                        <div>
                            <h4 class="alert-title">No KYC Application</h4>
                            <p class="mb-0">This user has not submitted a KYC application yet.</p>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Documents Table -->
                <?php if (!empty($user_documents)): ?>
                <div class="table-responsive">
                    <table class="table table-vcenter">
                        <thead>
                            <tr>
                                <th>Document Type</th>
                                <th>Document Name</th>
                                <th>File Info</th>
                                <th>Status</th>
                                <th>Upload Date</th>
                                <th>Verified By</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($user_documents as $doc): ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-<?php
                                        echo match($doc['document_type']) {
                                            'passport' => 'passport',
                                            'id_card' => 'id-card',
                                            'drivers_license' => 'car',
                                            'utility_bill' => 'file-invoice',
                                            'bank_statement' => 'university',
                                            'cheque' => 'money-check',
                                            'kyc_selfie' => 'camera',
                                            default => 'file'
                                        }; ?> me-2 text-muted"></i>
                                        <span><?php echo ucfirst(str_replace('_', ' ', $doc['document_type'])); ?></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 200px;" title="<?php echo htmlspecialchars($doc['document_name']); ?>">
                                        <?php echo htmlspecialchars($doc['document_name']); ?>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <small class="text-muted">
                                            <?php echo strtoupper(pathinfo($doc['file_path'], PATHINFO_EXTENSION)); ?> •
                                            <?php echo formatFileSize($doc['file_size']); ?>
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $doc['verification_status'] === 'approved' ? 'success' : ($doc['verification_status'] === 'rejected' ? 'danger' : 'warning'); ?>-lt">
                                        <?php echo ucfirst($doc['verification_status']); ?>
                                    </span>
                                    <?php if ($doc['expiry_date'] && strtotime($doc['expiry_date']) < time()): ?>
                                    <br><small class="text-danger">Expired</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div><?php echo formatDate($doc['created_at'], 'M j, Y'); ?></div>
                                    <small class="text-muted"><?php echo formatDate($doc['created_at'], 'g:i A'); ?></small>
                                </td>
                                <td>
                                    <?php if ($doc['verified_by']): ?>
                                    <div>Admin</div>
                                    <small class="text-muted"><?php echo $doc['verified_at'] ? formatDate($doc['verified_at'], 'M j, Y') : ''; ?></small>
                                    <?php else: ?>
                                    <span class="text-muted">Pending</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div class="btn-list">
                                        <button class="btn btn-sm btn-outline-primary" onclick="viewDocument(<?php echo $doc['id']; ?>)">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-success" onclick="approveDocument(<?php echo $doc['id']; ?>)">
                                            <i class="fas fa-check"></i>
                                        </button>
                                        <button class="btn btn-sm btn-outline-danger" onclick="rejectDocument(<?php echo $doc['id']; ?>)">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center text-muted py-4">
                    <i class="fas fa-file-alt mb-2" style="font-size: 2rem;"></i>
                    <p>No documents uploaded</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Beneficiaries & Support -->
<div class="row row-cards mt-4">
    <!-- Beneficiaries -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-users me-2"></i>
                    Beneficiaries
                </h3>
            </div>
            <div class="card-body">
                <?php if (!empty($beneficiaries)): ?>
                <div class="list-group list-group-flush">
                    <?php foreach ($beneficiaries as $beneficiary): ?>
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="fw-bold"><?php echo htmlspecialchars($beneficiary['name']); ?></div>
                                <small class="text-muted"><?php echo htmlspecialchars($beneficiary['account_number']); ?></small>
                                <div class="text-muted"><?php echo htmlspecialchars($beneficiary['bank_name']); ?></div>
                            </div>
                            <span class="badge bg-<?php echo $beneficiary['status'] === 'active' ? 'success' : 'danger'; ?>-lt">
                                <?php echo ucfirst($beneficiary['status']); ?>
                            </span>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php else: ?>
                <div class="text-center text-muted">
                    <i class="fas fa-users mb-2"></i>
                    <p>No beneficiaries added</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Support & Communication -->
<?php if (!empty($support_tickets)): ?>
<div class="row row-cards mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-headset me-2"></i>
                    Recent Support Tickets
                </h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-vcenter">
                        <thead>
                            <tr>
                                <th>Ticket ID</th>
                                <th>Subject</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Last Updated</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($support_tickets as $ticket): ?>
                            <tr>
                                <td>
                                    <span class="font-monospace">#<?php echo $ticket['id']; ?></span>
                                </td>
                                <td>
                                    <div class="text-truncate" style="max-width: 250px;" title="<?php echo htmlspecialchars($ticket['subject']); ?>">
                                        <?php echo htmlspecialchars($ticket['subject']); ?>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $ticket['priority'] === 'high' ? 'danger' : ($ticket['priority'] === 'medium' ? 'warning' : 'info'); ?>-lt">
                                        <?php echo ucfirst($ticket['priority']); ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $ticket['status'] === 'closed' ? 'success' : ($ticket['status'] === 'open' ? 'danger' : 'warning'); ?>-lt">
                                        <?php echo ucfirst($ticket['status']); ?>
                                    </span>
                                </td>
                                <td>
                                    <div><?php echo formatDate($ticket['created_at'], 'M j, Y'); ?></div>
                                    <small class="text-muted"><?php echo formatDate($ticket['created_at'], 'g:i A'); ?></small>
                                </td>
                                <td>
                                    <div><?php echo formatDate($ticket['updated_at'], 'M j, Y'); ?></div>
                                    <small class="text-muted"><?php echo formatDate($ticket['updated_at'], 'g:i A'); ?></small>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- OTP History Modal -->
<div class="modal modal-blur fade" id="otpHistoryModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">OTP History</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-vcenter">
                        <thead>
                            <tr>
                                <th>Code</th>
                                <th>Source</th>
                                <th>Created</th>
                                <th>Expires</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($otp_history as $otp): ?>
                            <tr>
                                <td>
                                    <span class="font-monospace"><?php echo htmlspecialchars($otp['otp_code']); ?></span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $otp['source'] === 'admin' ? 'warning' : 'primary'; ?>-lt">
                                        <?php echo ucfirst($otp['source'] ?? 'login'); ?>
                                    </span>
                                </td>
                                <td>
                                    <div><?php echo formatDate($otp['created_at'], 'M j, Y'); ?></div>
                                    <small class="text-muted"><?php echo formatDate($otp['created_at'], 'g:i A'); ?></small>
                                </td>
                                <td>
                                    <div><?php echo formatDate($otp['expires_at'], 'M j, Y'); ?></div>
                                    <small class="text-muted"><?php echo formatDate($otp['expires_at'], 'g:i A'); ?></small>
                                </td>
                                <td>
                                    <?php if ($otp['used']): ?>
                                    <span class="badge bg-success-lt">Used</span>
                                    <?php elseif (strtotime($otp['expires_at']) < time()): ?>
                                    <span class="badge bg-danger-lt">Expired</span>
                                    <?php else: ?>
                                    <span class="badge bg-warning-lt">Active</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
// OTP Management Functions
function generateOTPForUser(userId) {
    if (confirm('Generate a new OTP code for this user?')) {
        fetch('ajax/generate-otp.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                user_id: userId,
                source: 'admin'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('OTP generated successfully: ' + data.otp_code);
                location.reload();
            } else {
                alert('Error generating OTP: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while generating OTP');
        });
    }
}

function showOTPHistory() {
    $('#otpHistoryModal').modal('show');
}

// Document Management Functions
function uploadDocument() {
    // Create a simple upload modal
    const modal = `
        <div class="modal modal-blur fade" id="uploadDocumentModal" tabindex="-1" role="dialog" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Upload Document</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="uploadDocumentForm" enctype="multipart/form-data">
                            <input type="hidden" name="user_id" value="<?php echo $user['id']; ?>">
                            <div class="mb-3">
                                <label class="form-label">Document Type</label>
                                <select class="form-select" name="document_type" required>
                                    <option value="">Select document type</option>
                                    <option value="passport">Passport</option>
                                    <option value="id_card">ID Card</option>
                                    <option value="drivers_license">Driver's License</option>
                                    <option value="utility_bill">Utility Bill</option>
                                    <option value="bank_statement">Bank Statement</option>
                                    <option value="cheque">Cheque</option>
                                    <option value="kyc_selfie">KYC Selfie</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Document File</label>
                                <input type="file" class="form-control" name="document_file" accept=".pdf,.jpg,.jpeg,.png" required>
                                <small class="form-hint">Accepted formats: PDF, JPG, PNG (Max 10MB)</small>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Notes (Optional)</label>
                                <textarea class="form-control" name="notes" rows="3" placeholder="Additional notes about this document"></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="submitDocumentUpload()">Upload Document</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if present
    $('#uploadDocumentModal').remove();

    // Add modal to body and show
    $('body').append(modal);
    $('#uploadDocumentModal').modal('show');
}

function submitDocumentUpload() {
    const form = document.getElementById('uploadDocumentForm');
    const formData = new FormData(form);

    fetch('ajax/upload-document.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Document uploaded successfully!');
            $('#uploadDocumentModal').modal('hide');
            location.reload();
        } else {
            alert('Error uploading document: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while uploading the document');
    });
}

function viewDocument(documentId) {
    window.open('ajax/view-document.php?id=' + documentId, '_blank');
}

function approveDocument(documentId) {
    if (confirm('Approve this document?')) {
        updateDocumentStatus(documentId, 'approved');
    }
}

function rejectDocument(documentId) {
    const reason = prompt('Reason for rejection:');
    if (reason !== null) {
        updateDocumentStatus(documentId, 'rejected', reason);
    }
}

function updateDocumentStatus(documentId, status, reason = '') {
    fetch('ajax/update-document-status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            document_id: documentId,
            status: status,
            reason: reason
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Document status updated successfully!');
            location.reload();
        } else {
            alert('Error updating document status: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating document status');
    });
}

// Enhanced styling for better visual appeal
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.animationDelay = (index * 0.1) + 's';
        card.classList.add('fade-in');
    });

    // Add hover effects to list items
    const listItems = document.querySelectorAll('.list-group-item');
    listItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#f8f9fa';
        });
        item.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
});
</script>

<style>
/* Custom animations and styling */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out forwards;
}

/* Enhanced card styling */
.card {
    border: 1px solid #e9ecef;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Better list group styling */
.list-group-item {
    border: none;
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
}

.list-group-item:last-child {
    border-bottom: none;
}

/* Enhanced badge styling */
.badge {
    font-weight: 500;
    letter-spacing: 0.025em;
}

/* Better table styling */
.table th {
    font-weight: 600;
    color: #374151;
    border-bottom: 2px solid #e5e7eb;
}

/* Improved avatar gradient */
.avatar {
    background: linear-gradient(135deg, #206bc4, #1a5490) !important;
    color: white;
    font-weight: 600;
}

/* Enhanced alert styling */
.alert {
    border: none;
    border-radius: 8px;
}

/* Better modal styling */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

/* Responsive improvements */
@media (max-width: 768px) {
    .page-header .btn-list {
        flex-direction: column;
        gap: 0.5rem;
    }

    .page-header .btn-list .btn {
        width: 100%;
    }
}
</style>

<?php include 'includes/admin-footer.php'; ?>
<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Credit/Debit Testing';

// Test data
$test_results = [];

// Check if users exist
try {
    $db = getDB();
    $users_result = $db->query("SELECT id, first_name, last_name, username, account_number, balance FROM accounts WHERE is_admin = 0 ORDER BY first_name, last_name LIMIT 5");
    
    if ($users_result && $users_result->num_rows > 0) {
        $test_results['users_found'] = true;
        $test_results['user_count'] = $users_result->num_rows;
        $test_results['users'] = [];
        
        while ($user = $users_result->fetch_assoc()) {
            $test_results['users'][] = [
                'id' => $user['id'],
                'name' => $user['first_name'] . ' ' . $user['last_name'],
                'username' => $user['username'],
                'account_number' => $user['account_number'],
                'balance' => $user['balance']
            ];
        }
    } else {
        $test_results['users_found'] = false;
        $test_results['user_count'] = 0;
    }
    
    // Check if account_transactions table exists
    $table_check = $db->query("SHOW TABLES LIKE 'account_transactions'");
    $test_results['transactions_table_exists'] = $table_check->num_rows > 0;
    
    // Check recent transactions
    if ($test_results['transactions_table_exists']) {
        $recent_transactions = $db->query("SELECT COUNT(*) as count FROM account_transactions");
        $count_result = $recent_transactions->fetch_assoc();
        $test_results['transaction_count'] = $count_result['count'];
    } else {
        $test_results['transaction_count'] = 0;
    }
    
} catch (Exception $e) {
    $test_results['error'] = $e->getMessage();
}

include '../includes/admin_header.php';
?>

<div class="page-wrapper">
    <!-- Page header -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <div class="page-pretitle">
                        Administration
                    </div>
                    <h2 class="page-title">
                        Credit/Debit Testing
                    </h2>
                </div>
                <div class="col-auto ms-auto d-print-none">
                    <div class="btn-list">
                        <a href="accounts/" class="btn btn-primary">
                            <i class="fas fa-university me-2"></i>
                            Go to Credit/Debit Page
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Page body -->
    <div class="page-body">
        <div class="container-xl">
            <div class="row row-cards">
                <!-- System Status -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">System Status</h3>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>Users Available</span>
                                    <span class="badge bg-<?php echo $test_results['users_found'] ? 'green' : 'red'; ?>-lt">
                                        <?php echo $test_results['users_found'] ? 'Yes (' . $test_results['user_count'] . ')' : 'No'; ?>
                                    </span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>Transactions Table</span>
                                    <span class="badge bg-<?php echo $test_results['transactions_table_exists'] ? 'green' : 'red'; ?>-lt">
                                        <?php echo $test_results['transactions_table_exists'] ? 'Exists' : 'Missing'; ?>
                                    </span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    <span>Transaction Records</span>
                                    <span class="badge bg-blue-lt">
                                        <?php echo $test_results['transaction_count']; ?> records
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Test Users -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Available Test Users</h3>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($test_results['users'])): ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($test_results['users'] as $user): ?>
                                <div class="list-group-item">
                                    <div class="d-flex align-items-center">
                                        <span class="avatar avatar-sm me-3"><?php echo strtoupper(substr($user['name'], 0, 1)); ?></span>
                                        <div class="flex-fill">
                                            <div class="font-weight-medium"><?php echo htmlspecialchars($user['name']); ?></div>
                                            <div class="text-muted">@<?php echo htmlspecialchars($user['username']); ?> - <?php echo formatCurrency($user['balance']); ?></div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php else: ?>
                            <div class="empty">
                                <p class="empty-title">No users found</p>
                                <p class="empty-subtitle text-muted">
                                    Create some users first to test credit/debit functionality.
                                </p>
                                <div class="empty-action">
                                    <a href="add-user.php" class="btn btn-primary">
                                        <i class="fas fa-user-plus me-2"></i>
                                        Add User
                                    </a>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Testing Instructions -->
            <div class="row row-cards mt-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Testing Instructions</h3>
                        </div>
                        <div class="card-body">
                            <div class="steps">
                                <div class="step-item">
                                    <div class="h4">1. Navigate to Credit/Debit Page</div>
                                    <p>Click the "Go to Credit/Debit Page" button above or navigate to <code>admin/accounts/</code></p>
                                </div>
                                
                                <div class="step-item">
                                    <div class="h4">2. Test User Selection</div>
                                    <p>Select a user from the dropdown. You should see:</p>
                                    <ul>
                                        <li>User's full name and username in format: "John Doe (@john_doe) - $1,234.56"</li>
                                        <li>Account info panel should populate with user details</li>
                                        <li>Username should appear as "@username" in the account info</li>
                                    </ul>
                                </div>
                                
                                <div class="step-item">
                                    <div class="h4">3. Test Credit Operation</div>
                                    <p>Select "Credit" operation and test:</p>
                                    <ul>
                                        <li>Enter amount (e.g., 100.00)</li>
                                        <li>Select currency (USD)</li>
                                        <li>Choose category (e.g., "Deposit")</li>
                                        <li>Enter description</li>
                                        <li>Submit and verify success message</li>
                                    </ul>
                                </div>
                                
                                <div class="step-item">
                                    <div class="h4">4. Test Debit Operation</div>
                                    <p>Select "Debit" operation and test:</p>
                                    <ul>
                                        <li>Amount should be limited to user's current balance</li>
                                        <li>Enter amount less than balance</li>
                                        <li>Complete transaction</li>
                                        <li>Verify balance is reduced</li>
                                    </ul>
                                </div>
                                
                                <div class="step-item">
                                    <div class="h4">5. Verify Transaction History</div>
                                    <p>Check the "Recent Account Transactions" table:</p>
                                    <ul>
                                        <li>User's full name should appear in "Account" column</li>
                                        <li>Account number should be displayed</li>
                                        <li>Admin name should appear in "Processed By" column</li>
                                        <li>Reference number should be generated</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>

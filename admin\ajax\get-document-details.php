<?php
require_once '../../config/config.php';
requireAdmin();

header('Content-Type: application/json');

try {
    $document_id = intval($_GET['id'] ?? 0);
    
    if ($document_id <= 0) {
        throw new Exception('Invalid document ID');
    }
    
    $db = getDB();
    
    // Get document details with user information and verification details
    $query = "SELECT ud.*, 
              a.first_name, a.last_name, a.username, a.email,
              uploader.first_name as uploaded_by_name, uploader.last_name as uploaded_by_last_name,
              verifier.first_name as verified_by_name, verifier.last_name as verified_by_last_name
              FROM user_documents ud 
              LEFT JOIN accounts a ON ud.user_id = a.id 
              LEFT JOIN accounts uploader ON ud.uploaded_by = uploader.id 
              LEFT JOIN accounts verifier ON ud.verified_by = verifier.id 
              WHERE ud.id = ?";
    
    $result = $db->query($query, [$document_id]);
    
    if (!$result || $result->num_rows === 0) {
        throw new Exception('Document not found');
    }
    
    $document = $result->fetch_assoc();
    
    // Format the uploader and verifier names
    if ($document['uploaded_by_name']) {
        $document['uploaded_by_name'] = $document['uploaded_by_name'] . ' ' . $document['uploaded_by_last_name'];
    }
    if ($document['verified_by_name']) {
        $document['verified_by_name'] = $document['verified_by_name'] . ' ' . $document['verified_by_last_name'];
    }
    
    // Check if file exists
    $file_path = '../../' . ltrim($document['file_path'], '/');
    if (!file_exists($file_path)) {
        $document['file_exists'] = false;
        $document['file_error'] = 'File not found on server';
    } else {
        $document['file_exists'] = true;
        $document['actual_file_size'] = filesize($file_path);
    }
    
    // Log the view action
    error_log("Document details viewed: Document ID {$document_id}, Admin: {$_SESSION['user_id']}");
    
    echo json_encode([
        'success' => true,
        'document' => $document
    ]);
    
} catch (Exception $e) {
    error_log("Document details error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

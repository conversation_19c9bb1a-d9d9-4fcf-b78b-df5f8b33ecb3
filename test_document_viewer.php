<?php
require_once 'config/config.php';

// Test the document details endpoint
$document_id = 1; // Test with document ID 1

echo "<h2>Testing Document Viewer Functionality</h2>";

try {
    $db = getDB();
    
    // Test the query that the AJAX endpoint uses
    $query = "SELECT ud.*, 
              a.first_name, a.last_name, a.username, a.email,
              uploader.first_name as uploaded_by_name, uploader.last_name as uploaded_by_last_name,
              verifier.first_name as verified_by_name, verifier.last_name as verified_by_last_name
              FROM user_documents ud 
              LEFT JOIN accounts a ON ud.user_id = a.id 
              LEFT JOIN accounts uploader ON ud.uploaded_by = uploader.id 
              LEFT JOIN accounts verifier ON ud.verified_by = verifier.id 
              WHERE ud.id = ?";
    
    $result = $db->query($query, [$document_id]);
    
    if ($result && $result->num_rows > 0) {
        $document = $result->fetch_assoc();
        
        echo "<h3>✅ Document Found</h3>";
        echo "<pre>";
        print_r($document);
        echo "</pre>";
        
        // Check if file exists
        $file_path = ltrim($document['file_path'], '/');
        echo "<h3>File Path Check</h3>";
        echo "<p>File path: " . htmlspecialchars($file_path) . "</p>";
        
        if (file_exists($file_path)) {
            echo "<p>✅ File exists on server</p>";
            echo "<p>File size: " . filesize($file_path) . " bytes</p>";
        } else {
            echo "<p>❌ File does not exist on server</p>";
        }
        
    } else {
        echo "<h3>❌ No document found with ID: $document_id</h3>";
        
        // Show available documents
        $all_docs = $db->query("SELECT id, document_name, document_type, file_path FROM user_documents LIMIT 5");
        echo "<h4>Available Documents:</h4>";
        echo "<ul>";
        while ($doc = $all_docs->fetch_assoc()) {
            echo "<li>ID: {$doc['id']} - {$doc['document_name']} ({$doc['document_type']}) - {$doc['file_path']}</li>";
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<h3>❌ Error: " . $e->getMessage() . "</h3>";
}

echo "<br><br>";
echo "<h3>Test AJAX Endpoint</h3>";
echo "<p><a href='admin/ajax/get-document-details.php?id=1' target='_blank'>Test AJAX Endpoint (ID=1)</a></p>";
echo "<p><a href='admin/ajax/get-document-details.php?id=2' target='_blank'>Test AJAX Endpoint (ID=2)</a></p>";
echo "<p><a href='admin/ajax/get-document-details.php?id=3' target='_blank'>Test AJAX Endpoint (ID=3)</a></p>";

echo "<br><br>";
echo "<h3>Navigation</h3>";
echo "<p><a href='admin/view-user.php?id=5'>View User Page</a></p>";
echo "<p><a href='admin/users.php'>Users List</a></p>";
?>

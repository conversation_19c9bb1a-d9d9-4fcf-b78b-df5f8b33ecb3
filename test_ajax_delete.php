<?php
require_once 'config/config.php';

// Simulate admin session
$_SESSION['user_id'] = 1;
$_SESSION['username'] = 'admin';
$_SESSION['is_admin'] = true;

echo "<h2>AJAX Delete Test</h2>";
echo "<hr>";

// Create a test user first
try {
    $db = getDB();
    
    // Check if test user already exists
    $existing_test = $db->query("SELECT id FROM accounts WHERE username = 'ajax_test_user'");
    
    if ($existing_test->num_rows === 0) {
        // Create test user
        $test_account_number = generateAccountNumber();
        $test_password = hashPassword('testpass123');
        
        $insert_sql = "INSERT INTO accounts (account_number, username, password, email, first_name, last_name, account_type, balance, status, kyc_status) 
                       VALUES (?, 'ajax_test_user', ?, '<EMAIL>', 'AJAX', 'Test', 'savings', 50.00, 'active', 'verified')";
        
        $test_user_id = $db->insert($insert_sql, [$test_account_number, $test_password]);
        echo "✅ Created test user with ID: $test_user_id<br>";
    } else {
        $test_user = $existing_test->fetch_assoc();
        $test_user_id = $test_user['id'];
        echo "✅ Using existing test user with ID: $test_user_id<br>";
    }

    echo "<br><h3>Testing AJAX Delete Request:</h3>";
    
    // Simulate the AJAX request parameters
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_GET['id'] = $test_user_id;
    $_GET['ajax'] = '1';
    
    echo "📡 Simulating AJAX POST request to delete-user.php...<br>";
    echo "Parameters: id=$test_user_id, ajax=1<br><br>";
    
    // Capture output from delete-user.php
    ob_start();
    include 'admin/delete-user.php';
    $delete_response = ob_get_clean();
    
    echo "<h4>Response from delete-user.php:</h4>";
    echo "<pre style='background:#f5f5f5; padding:10px; border:1px solid #ddd;'>";
    echo htmlspecialchars($delete_response);
    echo "</pre>";
    
    // Try to decode as JSON
    $json_response = json_decode($delete_response, true);
    if ($json_response) {
        echo "<h4>Parsed JSON Response:</h4>";
        echo "<ul>";
        echo "<li><strong>Success:</strong> " . ($json_response['success'] ? 'Yes' : 'No') . "</li>";
        echo "<li><strong>Message:</strong> " . htmlspecialchars($json_response['message']) . "</li>";
        echo "</ul>";
        
        if ($json_response['success']) {
            echo "<h3 style='color:green;'>✅ DELETE FUNCTIONALITY IS WORKING!</h3>";
        } else {
            echo "<h3 style='color:red;'>❌ Delete failed: " . htmlspecialchars($json_response['message']) . "</h3>";
        }
    } else {
        echo "<h4 style='color:red;'>❌ Invalid JSON response or PHP error occurred</h4>";
    }

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
}

echo "<br><br><a href='admin/users.php'>← Back to User Management</a>";
?>

<?php
/**
 * Create Test User for Delete Functionality Testing
 */

require_once 'config/config.php';
requireAdmin();

try {
    $db = getDB();
    
    // Create a test user
    $test_user_data = [
        'account_number' => generateAccountNumber(),
        'username' => 'test_delete_user_' . time(),
        'password' => hashPassword('test123'),
        'email' => 'test_delete_' . time() . '@example.com',
        'first_name' => 'Test',
        'last_name' => 'DeleteUser',
        'phone' => '555-0123',
        'account_type' => 'savings',
        'balance' => 50.00, // Small balance for testing
        'status' => 'active',
        'kyc_status' => 'verified'
    ];
    
    $sql = "INSERT INTO accounts (account_number, username, password, email, first_name, last_name, phone, account_type, balance, status, kyc_status) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $test_user_data['account_number'],
        $test_user_data['username'],
        $test_user_data['password'],
        $test_user_data['email'],
        $test_user_data['first_name'],
        $test_user_data['last_name'],
        $test_user_data['phone'],
        $test_user_data['account_type'],
        $test_user_data['balance'],
        $test_user_data['status'],
        $test_user_data['kyc_status']
    ];
    
    $db->query($sql, $params);
    $test_user_id = $db->getConnection()->insert_id;
    
    // Add some related test data
    
    // Add a test beneficiary
    $beneficiary_sql = "INSERT INTO beneficiaries (user_id, name, account_number, bank_name, country) VALUES (?, ?, ?, ?, ?)";
    $db->query($beneficiary_sql, [$test_user_id, 'Test Beneficiary', '**********', 'Test Bank', 'USA']);
    
    // Add a test OTP (if table exists)
    try {
        $otp_sql = "INSERT INTO user_otps (user_id, otp_code, expires_at) VALUES (?, ?, DATE_ADD(NOW(), INTERVAL 10 MINUTE))";
        $db->query($otp_sql, [$test_user_id, '123456']);
    } catch (Exception $e) {
        // OTP table might not exist, that's okay
    }
    
    // Add a test ticket
    $ticket_sql = "INSERT INTO tickets (ticket_number, user_id, subject, message, status) VALUES (?, ?, ?, ?, ?)";
    $ticket_number = generateTicketNumber();
    $db->query($ticket_sql, [$ticket_number, $test_user_id, 'Test Ticket', 'This is a test ticket for deletion testing', 'open']);
    
    echo json_encode([
        'success' => true,
        'message' => 'Test user created successfully',
        'data' => [
            'user_id' => $test_user_id,
            'username' => $test_user_data['username'],
            'email' => $test_user_data['email'],
            'account_number' => $test_user_data['account_number'],
            'balance' => $test_user_data['balance']
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error creating test user: ' . $e->getMessage()
    ]);
}
?>

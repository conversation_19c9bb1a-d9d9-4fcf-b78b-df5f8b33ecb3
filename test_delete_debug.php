<?php
require_once 'config/config.php';

echo "<h2>Delete User Debug Test</h2>";
echo "<hr>";

// Test database connection
try {
    $db = getDB();
    echo "✅ Database connection successful<br><br>";
} catch (Exception $e) {
    echo "❌ Database connection failed: " . $e->getMessage() . "<br>";
    exit;
}

// Check current users
echo "<h3>Current Users in Database:</h3>";
try {
    $users_query = "SELECT id, username, email, first_name, last_name, is_admin FROM accounts ORDER BY id";
    $users_result = $db->query($users_query);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>Is Admin</th></tr>";
    
    while ($user = $users_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $user['id'] . "</td>";
        echo "<td>" . $user['username'] . "</td>";
        echo "<td>" . $user['email'] . "</td>";
        echo "<td>" . $user['first_name'] . " " . $user['last_name'] . "</td>";
        echo "<td>" . ($user['is_admin'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table><br>";
    
} catch (Exception $e) {
    echo "❌ Error fetching users: " . $e->getMessage() . "<br>";
}

// Check related tables
echo "<h3>Related Tables Check:</h3>";

// Check if user_otps table exists
try {
    $result = $db->query("SHOW TABLES LIKE 'user_otps'");
    if ($result->num_rows > 0) {
        echo "✅ user_otps table exists<br>";
        $count = $db->query("SELECT COUNT(*) as count FROM user_otps")->fetch_assoc()['count'];
        echo "&nbsp;&nbsp;&nbsp;Records: $count<br>";
    } else {
        echo "⚠️ user_otps table does not exist<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking user_otps: " . $e->getMessage() . "<br>";
}

// Check if account_transactions table exists
try {
    $result = $db->query("SHOW TABLES LIKE 'account_transactions'");
    if ($result->num_rows > 0) {
        echo "✅ account_transactions table exists<br>";
        $count = $db->query("SELECT COUNT(*) as count FROM account_transactions")->fetch_assoc()['count'];
        echo "&nbsp;&nbsp;&nbsp;Records: $count<br>";
    } else {
        echo "⚠️ account_transactions table does not exist<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking account_transactions: " . $e->getMessage() . "<br>";
}

// Check if transfers table exists
try {
    $result = $db->query("SHOW TABLES LIKE 'transfers'");
    if ($result->num_rows > 0) {
        echo "✅ transfers table exists<br>";
        $count = $db->query("SELECT COUNT(*) as count FROM transfers")->fetch_assoc()['count'];
        echo "&nbsp;&nbsp;&nbsp;Records: $count<br>";
    } else {
        echo "⚠️ transfers table does not exist<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking transfers: " . $e->getMessage() . "<br>";
}

// Check if virtual_cards table exists
try {
    $result = $db->query("SHOW TABLES LIKE 'virtual_cards'");
    if ($result->num_rows > 0) {
        echo "✅ virtual_cards table exists<br>";
        $count = $db->query("SELECT COUNT(*) as count FROM virtual_cards")->fetch_assoc()['count'];
        echo "&nbsp;&nbsp;&nbsp;Records: $count<br>";
    } else {
        echo "⚠️ virtual_cards table does not exist<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking virtual_cards: " . $e->getMessage() . "<br>";
}

echo "<br><h3>Test Delete Logic (Dry Run):</h3>";

// Test with a non-admin user (if exists)
try {
    $test_user_query = "SELECT id, username, email FROM accounts WHERE is_admin = 0 LIMIT 1";
    $test_user_result = $db->query($test_user_query);
    
    if ($test_user_result->num_rows > 0) {
        $test_user = $test_user_result->fetch_assoc();
        echo "🧪 Test user found: " . $test_user['username'] . " (ID: " . $test_user['id'] . ")<br>";
        
        // Test the actual delete query (but don't execute)
        $user_id = $test_user['id'];
        echo "📝 Would execute: DELETE FROM accounts WHERE id = $user_id AND is_admin = 0<br>";
        
        // Check what related records would be affected
        $related_checks = [
            'user_otps' => "SELECT COUNT(*) as count FROM user_otps WHERE user_id = $user_id",
            'account_transactions' => "SELECT COUNT(*) as count FROM account_transactions WHERE account_id = $user_id", 
            'transfers (sender)' => "SELECT COUNT(*) as count FROM transfers WHERE sender_id = $user_id",
            'transfers (recipient)' => "SELECT COUNT(*) as count FROM transfers WHERE recipient_id = $user_id",
            'virtual_cards' => "SELECT COUNT(*) as count FROM virtual_cards WHERE user_id = $user_id"
        ];
        
        foreach ($related_checks as $table => $query) {
            try {
                $result = $db->query($query);
                if ($result) {
                    $count = $result->fetch_assoc()['count'];
                    echo "&nbsp;&nbsp;&nbsp;$table: $count records would be affected<br>";
                }
            } catch (Exception $e) {
                echo "&nbsp;&nbsp;&nbsp;$table: Table doesn't exist or query failed<br>";
            }
        }
        
    } else {
        echo "⚠️ No non-admin users found to test with<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error in delete test: " . $e->getMessage() . "<br>";
}

echo "<br><h3>Recommendations:</h3>";
echo "1. Fix any missing tables identified above<br>";
echo "2. Ensure proper foreign key relationships<br>";
echo "3. Test the delete functionality with the corrected file<br>";
?>

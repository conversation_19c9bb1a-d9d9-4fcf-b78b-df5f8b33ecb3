<?php
require_once '../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    redirect('../auth/login.php');
}

$page_title = 'Transfers';

// Define page actions
$page_actions = [
    [
        'url' => 'index.php',
        'label' => 'Back to Dashboard',
        'icon' => 'fas fa-arrow-left'
    ]
];

include 'includes/admin-header.php';
?>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" style="margin-bottom: 1rem;">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
        <li class="breadcrumb-item active" aria-current="page">Transfers</li>
    </ol>
</nav>

<div class="row row-cards">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-paper-plane me-2"></i>
                    Transfer Management
                </h3>
            </div>
            <div class="card-body">
                <div class="empty">
                    <div class="empty-img">
                        <i class="fas fa-paper-plane" style="font-size: 4rem; color: #6c757d;"></i>
                    </div>
                    <p class="empty-title">Transfer Management</p>
                    <p class="empty-subtitle text-muted">
                        This page will contain transfer management functionality including:
                    </p>
                    <ul class="text-start" style="max-width: 400px; margin: 0 auto;">
                        <li>View all user transfers</li>
                        <li>Monitor transfer status and history</li>
                        <li>Approve/reject pending transfers</li>
                        <li>Set transfer limits and restrictions</li>
                        <li>Generate transfer reports</li>
                    </ul>
                    <div class="empty-action mt-4">
                        <a href="users.php" class="btn btn-primary">
                            <i class="fas fa-users me-2"></i>
                            Manage Users Instead
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/admin-footer.php'; ?>

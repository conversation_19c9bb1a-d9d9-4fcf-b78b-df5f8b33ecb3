<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Database Update';

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_database'])) {
    $success = false;
    $error = '';
    
    try {
        // Add missing columns to accounts table
        $queries = [
            "ALTER TABLE accounts ADD COLUMN occupation VARCHAR(100) AFTER date_of_birth",
            "ALTER TABLE accounts ADD COLUMN marital_status ENUM('single', 'married', 'divorced', 'widowed') DEFAULT 'single' AFTER occupation",
            "ALTER TABLE accounts ADD COLUMN gender ENUM('male', 'female', 'other') DEFAULT 'male' AFTER marital_status",
            "ALTER TABLE accounts ADD COLUMN currency VARCHAR(3) DEFAULT 'USD' AFTER gender"
        ];
        
        foreach ($queries as $query) {
            try {
                $stmt = $pdo->prepare($query);
                $stmt->execute();
            } catch (PDOException $e) {
                // Column might already exist, check if it's a duplicate column error
                if (strpos($e->getMessage(), 'Duplicate column name') === false) {
                    throw $e;
                }
            }
        }
        
        // Update existing records to have default values
        $updateQuery = "UPDATE accounts SET 
                        occupation = COALESCE(occupation, 'Not Specified'),
                        marital_status = COALESCE(marital_status, 'single'),
                        gender = COALESCE(gender, 'male'),
                        currency = COALESCE(currency, 'USD')
                        WHERE occupation IS NULL OR marital_status IS NULL OR gender IS NULL OR currency IS NULL";
        
        $stmt = $pdo->prepare($updateQuery);
        $stmt->execute();
        
        $success = true;
        
    } catch (PDOException $e) {
        $error = "Database error: " . $e->getMessage();
    }
}

include '../includes/admin_header.php';
?>

<div class="page-wrapper">
    <!-- Page header -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <div class="page-pretitle">
                        Administration
                    </div>
                    <h2 class="page-title">
                        Database Update
                    </h2>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Page body -->
    <div class="page-body">
        <div class="container-xl">
            <div class="row row-cards">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Add Missing User Fields</h3>
                        </div>
                        <div class="card-body">
                            <?php if (isset($success) && $success): ?>
                                <div class="alert alert-success">
                                    <h4>Database Updated Successfully!</h4>
                                    <p>The following fields have been added to the accounts table:</p>
                                    <ul>
                                        <li>Occupation</li>
                                        <li>Marital Status</li>
                                        <li>Gender</li>
                                        <li>Currency</li>
                                    </ul>
                                    <p>You can now use the enhanced <a href="add-user.php">Add User form</a>.</p>
                                </div>
                            <?php elseif (isset($error) && $error): ?>
                                <div class="alert alert-danger">
                                    <h4>Error</h4>
                                    <p><?php echo htmlspecialchars($error); ?></p>
                                </div>
                            <?php endif; ?>
                            
                            <p>This will add the following fields to the accounts table:</p>
                            <ul>
                                <li><strong>Occupation</strong> - VARCHAR(100)</li>
                                <li><strong>Marital Status</strong> - ENUM('single', 'married', 'divorced', 'widowed')</li>
                                <li><strong>Gender</strong> - ENUM('male', 'female', 'other')</li>
                                <li><strong>Currency</strong> - VARCHAR(3) with default 'USD'</li>
                            </ul>
                            
                            <form method="POST" action="">
                                <button type="submit" name="update_database" class="btn btn-primary" onclick="return confirm('Are you sure you want to update the database structure?')">
                                    <i class="fas fa-database me-2"></i>
                                    Update Database
                                </button>
                                <a href="add-user.php" class="btn btn-secondary ms-2">
                                    Go to Add User Form
                                </a>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>

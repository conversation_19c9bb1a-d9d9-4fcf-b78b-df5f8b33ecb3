<?php
require_once '../config/config.php';

echo "<h1>🧪 Comprehensive User Delete Test</h1>";

// Test configuration
$test_user_data = [
    'username' => 'testuser_' . time(),
    'email' => 'testdelete_' . time() . '@example.com',
    'password' => 'TestPassword123!',
    'first_name' => 'Test',
    'last_name' => 'User',
    'phone' => '+**********',
    'address' => '123 Test Street',
    'currency' => 'USD',
    'account_type' => 'savings',
    'initial_balance' => 100.00
];

$created_user_id = null;

try {
    $db = getDB();
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h2>📋 Test Plan</h2>";
    echo "<ol>";
    echo "<li>Create a test user</li>";
    echo "<li>Verify user creation</li>";
    echo "<li>Test delete functionality via admin interface simulation</li>";
    echo "<li>Verify user deletion</li>";
    echo "<li>Test edge cases</li>";
    echo "</ol>";
    echo "</div>";
    
    // Step 1: Create test user
    echo "<h2>Step 1: Creating Test User</h2>";
    
    // Generate account number
    function generateTestAccountNumber() {
        return 'ACC' . str_pad(mt_rand(1, 999999), 6, '0', STR_PAD_LEFT);
    }
    
    $account_number = generateTestAccountNumber();
    $hashed_password = password_hash($test_user_data['password'], PASSWORD_DEFAULT);
    
    $sql = "INSERT INTO accounts (
                account_number, username, password, email, first_name, last_name,
                phone, address, currency, account_type, balance, status, kyc_status, is_admin
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active', 'pending', 0)";
    
    $params = [
        $account_number,
        $test_user_data['username'],
        $hashed_password,
        $test_user_data['email'],
        $test_user_data['first_name'],
        $test_user_data['last_name'],
        $test_user_data['phone'],
        $test_user_data['address'],
        $test_user_data['currency'],
        $test_user_data['account_type'],
        $test_user_data['initial_balance']
    ];
    
    $created_user_id = $db->insert($sql, $params);
    
    if ($created_user_id) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>✅ User Created Successfully!</h4>";
        echo "<p><strong>User ID:</strong> $created_user_id</p>";
        echo "<p><strong>Username:</strong> {$test_user_data['username']}</p>";
        echo "<p><strong>Email:</strong> {$test_user_data['email']}</p>";
        echo "<p><strong>Account Number:</strong> $account_number</p>";
        echo "</div>";
    } else {
        throw new Exception("Failed to create test user");
    }
    
    // Step 2: Verify user creation
    echo "<h2>Step 2: Verifying User Creation</h2>";
    
    $verify_result = $db->query("SELECT * FROM accounts WHERE id = ?", [$created_user_id]);
    
    if ($verify_result->num_rows > 0) {
        $user = $verify_result->fetch_assoc();
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>✅ User Verification Successful!</h4>";
        echo "<p>User exists in database with all expected data</p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Field</th><th>Value</th></tr>";
        echo "<tr><td>ID</td><td>{$user['id']}</td></tr>";
        echo "<tr><td>Username</td><td>{$user['username']}</td></tr>";
        echo "<tr><td>Email</td><td>{$user['email']}</td></tr>";
        echo "<tr><td>Account Number</td><td>{$user['account_number']}</td></tr>";
        echo "<tr><td>Balance</td><td>\${$user['balance']}</td></tr>";
        echo "<tr><td>Status</td><td>{$user['status']}</td></tr>";
        echo "<tr><td>Is Admin</td><td>" . ($user['is_admin'] ? 'Yes' : 'No') . "</td></tr>";
        echo "</table>";
        echo "</div>";
    } else {
        throw new Exception("User verification failed - user not found in database");
    }
    
    // Step 3: Test delete functionality
    echo "<h2>Step 3: Testing Delete Functionality</h2>";
    
    echo "<h3>3.1: Simulating Admin Interface Delete</h3>";
    
    // Simulate the delete process from admin interface
    echo "<p>Simulating delete request for user ID: $created_user_id</p>";
    
    // First, get user details for logging (like the admin interface does)
    $user_query = "SELECT id, username, email, first_name, last_name, account_number FROM accounts WHERE id = ? AND is_admin = 0";
    $user_result = $db->query($user_query, [$created_user_id]);
    
    if ($user_result->num_rows === 0) {
        throw new Exception('User not found or cannot delete admin users.');
    }
    
    $user_to_delete = $user_result->fetch_assoc();
    echo "<p>✅ User found for deletion: {$user_to_delete['username']} ({$user_to_delete['email']})</p>";
    
    // Start transaction (like the admin delete does)
    $db->query("START TRANSACTION");
    
    try {
        echo "<h4>Deleting related records...</h4>";
        
        // Delete OTP records (only if table exists)
        try {
            $affected = $db->delete("DELETE FROM user_otps WHERE user_id = ?", [$created_user_id]);
            echo "<p>✅ OTP records deleted (affected rows: $affected)</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ OTP table issue (expected if table doesn't exist): " . $e->getMessage() . "</p>";
        }

        // Delete transaction history (only if table exists)
        try {
            $affected = $db->delete("DELETE FROM transactions WHERE account_id = ?", [$created_user_id]);
            echo "<p>✅ Transaction records deleted (affected rows: $affected)</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ Transactions table issue (expected if table doesn't exist): " . $e->getMessage() . "</p>";
        }

        // Delete virtual cards (only if table exists)
        try {
            $affected = $db->delete("DELETE FROM virtual_cards WHERE user_id = ?", [$created_user_id]);
            echo "<p>✅ Virtual cards deleted (affected rows: $affected)</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ Virtual cards table issue (expected if table doesn't exist): " . $e->getMessage() . "</p>";
        }

        // Delete user sessions (only if table exists)
        try {
            $affected = $db->delete("DELETE FROM user_sessions WHERE user_id = ?", [$created_user_id]);
            echo "<p>✅ User sessions deleted (affected rows: $affected)</p>";
        } catch (Exception $e) {
            echo "<p>⚠️ User sessions table issue (expected if table doesn't exist): " . $e->getMessage() . "</p>";
        }

        // Finally, delete the user account
        echo "<h4>Deleting main user account...</h4>";
        $affected_rows = $db->delete("DELETE FROM accounts WHERE id = ? AND is_admin = 0", [$created_user_id]);

        if ($affected_rows > 0) {
            // Commit transaction
            $db->query("COMMIT");
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h4>✅ Delete Operation Successful!</h4>";
            echo "<p>User '{$user_to_delete['username']}' has been permanently deleted.</p>";
            echo "<p>Affected rows: $affected_rows</p>";
            echo "</div>";
            
        } else {
            // Rollback transaction
            $db->query("ROLLBACK");
            throw new Exception('Failed to delete user. No rows affected.');
        }
        
    } catch (Exception $e) {
        // Rollback transaction on error
        $db->query("ROLLBACK");
        throw $e;
    }
    
    // Step 4: Verify deletion
    echo "<h2>Step 4: Verifying User Deletion</h2>";
    
    $verify_delete = $db->query("SELECT * FROM accounts WHERE id = ?", [$created_user_id]);
    
    if ($verify_delete->num_rows === 0) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>✅ Deletion Verification Successful!</h4>";
        echo "<p>User has been completely removed from the database</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>❌ Deletion Verification Failed!</h4>";
        echo "<p>User still exists in database</p>";
        echo "</div>";
    }
    
    // Step 5: Test edge cases
    echo "<h2>Step 5: Testing Edge Cases</h2>";
    
    echo "<h3>5.1: Attempting to delete non-existent user</h3>";
    $fake_id = 999999;
    $fake_result = $db->delete("DELETE FROM accounts WHERE id = ? AND is_admin = 0", [$fake_id]);
    echo "<p>Delete non-existent user (ID: $fake_id): $fake_result affected rows ✅</p>";
    
    echo "<h3>5.2: Current users in database</h3>";
    $all_users = $db->query("SELECT id, username, email, is_admin FROM accounts ORDER BY id");
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f2f2f2;'><th>ID</th><th>Username</th><th>Email</th><th>Is Admin</th></tr>";
    
    while ($row = $all_users->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['username']}</td>";
        echo "<td>{$row['email']}</td>";
        echo "<td>" . ($row['is_admin'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h2>🎉 All Tests Completed Successfully!</h2>";
    echo "<p>The delete user functionality is working correctly.</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>❌ Test Failed!</h4>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
    
    // Cleanup: Try to delete the test user if it was created
    if ($created_user_id) {
        try {
            echo "<h3>🧹 Cleanup: Attempting to delete test user</h3>";
            $cleanup_result = $db->delete("DELETE FROM accounts WHERE id = ?", [$created_user_id]);
            echo "<p>Cleanup result: $cleanup_result affected rows</p>";
        } catch (Exception $cleanup_e) {
            echo "<p>Cleanup failed: " . $cleanup_e->getMessage() . "</p>";
        }
    }
}
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f8f9fa; 
    line-height: 1.6;
}
h1, h2, h3 { color: #333; }
h1 { border-bottom: 3px solid #007bff; padding-bottom: 10px; }
h2 { border-bottom: 1px solid #dee2e6; padding-bottom: 5px; }
table { 
    border-collapse: collapse; 
    width: 100%; 
    margin: 10px 0; 
    background: white;
}
th, td { 
    border: 1px solid #ddd; 
    padding: 8px; 
    text-align: left; 
}
th { 
    background-color: #f2f2f2; 
    font-weight: bold;
}
ol { 
    padding-left: 20px; 
}
p { 
    margin: 5px 0; 
}
</style>

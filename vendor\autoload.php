<?php
/**
 * Simple autoloader for PHPMailer
 */

// Define the base directory for PHPMailer
define('PHPMAILER_BASE_DIR', __DIR__ . '/phpmailer/phpmailer/src/');

// Autoloader function
spl_autoload_register(function ($class) {
    // Check if this is a PHPMailer class
    if (strpos($class, '<PERSON>HPMailer\\PHPMailer\\') === 0) {
        // Remove the namespace prefix
        $class = str_replace('PHPMailer\\PHPMailer\\', '', $class);
        
        // Convert class name to file path
        $file = PHPMAILER_BASE_DIR . $class . '.php';
        
        // Include the file if it exists
        if (file_exists($file)) {
            require_once $file;
        }
    }
});

// For backward compatibility, also check for direct PHPMailer class usage
if (!class_exists('PHPMailer\\PHPMailer\\PHPMailer')) {
    // Try to include the main PHPMailer files directly
    $phpmailer_files = [
        PHPMAILER_BASE_DIR . 'PHPMailer.php',
        PHPMAILER_BASE_DIR . 'SMTP.php',
        PHPMAILER_BASE_DIR . 'Exception.php'
    ];
    
    foreach ($phpmailer_files as $file) {
        if (file_exists($file)) {
            require_once $file;
        }
    }
}
?>

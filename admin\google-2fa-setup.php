<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Google 2FA Setup';

// Include Google2FA class
require_once '../vendor/GoogleAuthenticator/Google2FA.php';
$google2fa = new Google2FA();

// Initialize database connection
$db = getDB();

$success = '';
$error = '';
$qr_code_url = '';
$secret = '';
$selected_user = null;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $db = getDB();
        
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'generate_qr':
                    $user_id = intval($_POST['user_id']);
                    if ($user_id > 0) {
                        // Get user details
                        $user_query = "SELECT * FROM accounts WHERE id = ? AND is_admin = 0";
                        $user_result = $db->query($user_query, [$user_id]);
                        
                        if ($user_result && $user_result->num_rows === 1) {
                            $selected_user = $user_result->fetch_assoc();
                            
                            // Generate new secret
                            $secret = $google2fa->generateSecretKey();
                            
                            // Generate QR code URL
                            $company = 'SecureBank Online';
                            $holder = $selected_user['email'];
                            $qr_code_url = $google2fa->getQRCodeUrl($company, $holder, $secret);
                            
                            // Store secret temporarily in session for verification
                            $_SESSION['temp_2fa_secret'] = $secret;
                            $_SESSION['temp_2fa_user_id'] = $user_id;
                        } else {
                            $error = 'User not found.';
                        }
                    }
                    break;
                    
                case 'verify_setup':
                    $verification_code = $_POST['verification_code'];
                    $user_id = $_SESSION['temp_2fa_user_id'] ?? 0;
                    $secret = $_SESSION['temp_2fa_secret'] ?? '';
                    
                    if ($user_id && $secret && $verification_code) {
                        // Verify the code
                        if ($google2fa->verifyKey($secret, $verification_code)) {
                            // Save the secret to database
                            $update_sql = "UPDATE user_security_settings SET 
                                          google_2fa_enabled = 1, 
                                          google_2fa_secret = ?, 
                                          updated_by = ?
                                          WHERE user_id = ?";
                            
                            $result = $db->query($update_sql, [$secret, $_SESSION['user_id'], $user_id]);
                            
                            if ($result) {
                                // Clear temporary session data
                                unset($_SESSION['temp_2fa_secret']);
                                unset($_SESSION['temp_2fa_user_id']);
                                
                                $success = 'Google 2FA has been successfully enabled for the user!';
                                $qr_code_url = '';
                                $secret = '';
                                $selected_user = null;
                            } else {
                                $error = 'Failed to save 2FA settings to database.';
                            }
                        } else {
                            $error = 'Invalid verification code. Please try again.';
                            // Keep the QR code visible for retry
                            $user_id = $_SESSION['temp_2fa_user_id'];
                            $secret = $_SESSION['temp_2fa_secret'];
                            
                            $user_query = "SELECT * FROM accounts WHERE id = ? AND is_admin = 0";
                            $user_result = $db->query($user_query, [$user_id]);
                            if ($user_result && $user_result->num_rows === 1) {
                                $selected_user = $user_result->fetch_assoc();
                                $company = 'SecureBank Online';
                                $holder = $selected_user['email'];
                                $qr_code_url = $google2fa->getQRCodeUrl($company, $holder, $secret);
                            }
                        }
                    } else {
                        $error = 'Missing verification data. Please start over.';
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        error_log("Google 2FA Setup Error: " . $e->getMessage());
        $error = 'An error occurred while setting up 2FA.';
    }
}

// Get all users for selection
$users_query = "SELECT a.id, a.username, a.first_name, a.last_name, a.email,
                       uss.google_2fa_enabled
                FROM accounts a 
                LEFT JOIN user_security_settings uss ON a.id = uss.user_id 
                WHERE a.is_admin = 0 
                ORDER BY a.first_name, a.last_name";
$users_result = $db->query($users_query);
$users = [];
if ($users_result) {
    while ($row = $users_result->fetch_assoc()) {
        $users[] = $row;
    }
}

include '../admin/includes/header.php';
?>

<div class="page-wrapper">
    <?php include '../admin/includes/sidebar.php'; ?>
    
    <div class="page-content">
        <div class="container-xl">
            <!-- Page header -->
            <div class="page-header d-print-none">
                <div class="row align-items-center">
                    <div class="col">
                        <h2 class="page-title">Google 2FA Setup</h2>
                        <div class="text-muted mt-1">Help users set up Google Authenticator</div>
                    </div>
                    <div class="col-auto ms-auto d-print-none">
                        <div class="btn-list">
                            <a href="configure-2fa.php" class="btn btn-outline-primary">
                                <i class="ti ti-arrow-left me-1"></i>
                                Back to 2FA Config
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Alerts -->
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible" role="alert">
                    <div class="d-flex">
                        <div><i class="ti ti-check alert-icon"></i></div>
                        <div><?php echo htmlspecialchars($success); ?></div>
                    </div>
                    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible" role="alert">
                    <div class="d-flex">
                        <div><i class="ti ti-alert-circle alert-icon"></i></div>
                        <div><?php echo htmlspecialchars($error); ?></div>
                    </div>
                    <a class="btn-close" data-bs-dismiss="alert" aria-label="close"></a>
                </div>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Setup Google Authenticator</h3>
                        </div>
                        <div class="card-body">
                            <?php if (!$qr_code_url): ?>
                                <!-- Step 1: Select User -->
                                <div class="mb-4">
                                    <h4>Step 1: Select User</h4>
                                    <p class="text-muted">Choose a user to set up Google Authenticator for.</p>
                                </div>
                                
                                <form method="POST">
                                    <input type="hidden" name="action" value="generate_qr">
                                    <div class="mb-3">
                                        <label class="form-label">Select User</label>
                                        <select name="user_id" class="form-select" required>
                                            <option value="">Choose a user...</option>
                                            <?php foreach ($users as $user): ?>
                                                <option value="<?php echo $user['id']; ?>" <?php echo $user['google_2fa_enabled'] ? 'disabled' : ''; ?>>
                                                    <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name'] . ' (' . $user['username'] . ')'); ?>
                                                    <?php if ($user['google_2fa_enabled']): ?>
                                                        - 2FA Already Enabled
                                                    <?php endif; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-qrcode me-1"></i>
                                        Generate QR Code
                                    </button>
                                </form>
                            <?php else: ?>
                                <!-- Step 2: Show QR Code and Verify -->
                                <div class="mb-4">
                                    <h4>Step 2: Scan QR Code</h4>
                                    <p class="text-muted">
                                        Setting up 2FA for: <strong><?php echo htmlspecialchars($selected_user['first_name'] . ' ' . $selected_user['last_name']); ?></strong>
                                    </p>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="text-center mb-4">
                                            <img src="<?php echo htmlspecialchars($qr_code_url); ?>" alt="QR Code" class="img-fluid" style="max-width: 200px;">
                                        </div>
                                        
                                        <div class="alert alert-info">
                                            <h4>Instructions:</h4>
                                            <ol class="mb-0">
                                                <li>Install Google Authenticator app on your phone</li>
                                                <li>Open the app and tap "+"</li>
                                                <li>Select "Scan QR code"</li>
                                                <li>Scan the QR code shown above</li>
                                                <li>Enter the 6-digit code from the app below</li>
                                            </ol>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Secret Key (Manual Entry)</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" value="<?php echo htmlspecialchars($secret); ?>" readonly>
                                                <button class="btn btn-outline-secondary" type="button" onclick="copyToClipboard('<?php echo htmlspecialchars($secret); ?>')">
                                                    <i class="ti ti-copy"></i>
                                                </button>
                                            </div>
                                            <small class="form-hint">Use this if you can't scan the QR code</small>
                                        </div>
                                        
                                        <form method="POST">
                                            <input type="hidden" name="action" value="verify_setup">
                                            <div class="mb-3">
                                                <label class="form-label">Verification Code</label>
                                                <input type="text" name="verification_code" class="form-control" placeholder="Enter 6-digit code" maxlength="6" required>
                                                <small class="form-hint">Enter the 6-digit code from Google Authenticator</small>
                                            </div>
                                            <div class="btn-list">
                                                <button type="submit" class="btn btn-success">
                                                    <i class="ti ti-check me-1"></i>
                                                    Verify & Enable 2FA
                                                </button>
                                                <a href="google-2fa-setup.php" class="btn btn-secondary">
                                                    <i class="ti ti-x me-1"></i>
                                                    Cancel
                                                </a>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const btn = event.target.closest('button');
        const originalHtml = btn.innerHTML;
        btn.innerHTML = '<i class="ti ti-check"></i>';
        btn.classList.add('btn-success');
        btn.classList.remove('btn-outline-secondary');
        
        setTimeout(() => {
            btn.innerHTML = originalHtml;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    });
}
</script>

<?php include '../admin/includes/footer.php'; ?>

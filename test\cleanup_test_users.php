<?php
require_once '../config/config.php';

echo "<h1>🧹 Test User Cleanup Utility</h1>";

try {
    $db = getDB();
    
    // Find all test users (those with usernames starting with test patterns)
    $test_patterns = ['testuser_%', 'browsertest_%', 'test_%', 'demo_%'];
    $test_users = [];
    
    foreach ($test_patterns as $pattern) {
        $result = $db->query("SELECT id, username, email, first_name, last_name, account_number, balance FROM accounts WHERE username LIKE ? AND is_admin = 0", [$pattern]);
        
        while ($row = $result->fetch_assoc()) {
            $test_users[] = $row;
        }
    }
    
    if (empty($test_users)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h3>✅ No Test Users Found</h3>";
        echo "<p>No test users matching the patterns were found in the database.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h3>⚠️ Found " . count($test_users) . " Test Users</h3>";
        echo "<p>The following test users were found and can be deleted:</p>";
        echo "</div>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0; background: white;'>";
        echo "<tr style='background: #f2f2f2;'>";
        echo "<th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>Account Number</th><th>Balance</th><th>Action</th>";
        echo "</tr>";
        
        foreach ($test_users as $user) {
            echo "<tr>";
            echo "<td>{$user['id']}</td>";
            echo "<td>{$user['username']}</td>";
            echo "<td>{$user['email']}</td>";
            echo "<td>{$user['first_name']} {$user['last_name']}</td>";
            echo "<td>{$user['account_number']}</td>";
            echo "<td>\${$user['balance']}</td>";
            echo "<td><a href='?delete_id={$user['id']}' onclick='return confirm(\"Delete user {$user['username']}?\")' style='color: red;'>Delete</a></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>🔧 Bulk Actions</h4>";
        echo "<p><a href='?delete_all=1' onclick='return confirm(\"Are you sure you want to delete ALL test users? This cannot be undone!\")' style='background: #dc3545; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Delete All Test Users</a></p>";
        echo "</div>";
    }
    
    // Handle deletion requests
    if (isset($_GET['delete_id'])) {
        $user_id = (int)$_GET['delete_id'];
        
        echo "<h2>🗑️ Deleting User ID: $user_id</h2>";
        
        // Get user details first
        $user_result = $db->query("SELECT id, username, email, first_name, last_name, account_number FROM accounts WHERE id = ? AND is_admin = 0", [$user_id]);
        
        if ($user_result->num_rows === 0) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h4>❌ Error</h4>";
            echo "<p>User not found or is an admin user.</p>";
            echo "</div>";
        } else {
            $user = $user_result->fetch_assoc();
            
            // Start transaction
            $db->query("START TRANSACTION");
            
            try {
                // Delete related records first
                echo "<h3>Deleting related records...</h3>";
                
                // Delete OTP records
                try {
                    $affected = $db->delete("DELETE FROM user_otps WHERE user_id = ?", [$user_id]);
                    echo "<p>✅ OTP records deleted (affected rows: $affected)</p>";
                } catch (Exception $e) {
                    echo "<p>⚠️ OTP table issue: " . $e->getMessage() . "</p>";
                }

                // Delete transaction history
                try {
                    $affected = $db->delete("DELETE FROM transactions WHERE account_id = ?", [$user_id]);
                    echo "<p>✅ Transaction records deleted (affected rows: $affected)</p>";
                } catch (Exception $e) {
                    echo "<p>⚠️ Transactions table issue: " . $e->getMessage() . "</p>";
                }

                // Delete virtual cards
                try {
                    $affected = $db->delete("DELETE FROM virtual_cards WHERE user_id = ?", [$user_id]);
                    echo "<p>✅ Virtual cards deleted (affected rows: $affected)</p>";
                } catch (Exception $e) {
                    echo "<p>⚠️ Virtual cards table issue: " . $e->getMessage() . "</p>";
                }

                // Delete user sessions
                try {
                    $affected = $db->delete("DELETE FROM user_sessions WHERE user_id = ?", [$user_id]);
                    echo "<p>✅ User sessions deleted (affected rows: $affected)</p>";
                } catch (Exception $e) {
                    echo "<p>⚠️ User sessions table issue: " . $e->getMessage() . "</p>";
                }

                // Finally, delete the user account
                $affected_rows = $db->delete("DELETE FROM accounts WHERE id = ? AND is_admin = 0", [$user_id]);

                if ($affected_rows > 0) {
                    $db->query("COMMIT");
                    
                    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                    echo "<h4>✅ User Deleted Successfully!</h4>";
                    echo "<p>User '{$user['username']}' ({$user['email']}) has been permanently deleted.</p>";
                    echo "<p>Account Number: {$user['account_number']}</p>";
                    echo "</div>";
                    
                    echo "<p><a href='cleanup_test_users.php'>← Back to Cleanup Utility</a></p>";
                    
                } else {
                    $db->query("ROLLBACK");
                    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
                    echo "<h4>❌ Delete Failed</h4>";
                    echo "<p>No rows were affected. User may not exist.</p>";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                $db->query("ROLLBACK");
                throw $e;
            }
        }
    }
    
    // Handle bulk deletion
    if (isset($_GET['delete_all']) && $_GET['delete_all'] == '1') {
        echo "<h2>🗑️ Bulk Deleting All Test Users</h2>";
        
        $deleted_count = 0;
        $errors = [];
        
        foreach ($test_patterns as $pattern) {
            try {
                $result = $db->query("SELECT id, username FROM accounts WHERE username LIKE ? AND is_admin = 0", [$pattern]);
                
                while ($row = $result->fetch_assoc()) {
                    $user_id = $row['id'];
                    $username = $row['username'];
                    
                    // Start transaction for each user
                    $db->query("START TRANSACTION");
                    
                    try {
                        // Delete related records
                        $db->delete("DELETE FROM user_otps WHERE user_id = ?", [$user_id]);
                        $db->delete("DELETE FROM transactions WHERE account_id = ?", [$user_id]);
                        $db->delete("DELETE FROM virtual_cards WHERE user_id = ?", [$user_id]);
                        $db->delete("DELETE FROM user_sessions WHERE user_id = ?", [$user_id]);
                        
                        // Delete user
                        $affected = $db->delete("DELETE FROM accounts WHERE id = ? AND is_admin = 0", [$user_id]);
                        
                        if ($affected > 0) {
                            $db->query("COMMIT");
                            $deleted_count++;
                            echo "<p>✅ Deleted: $username (ID: $user_id)</p>";
                        } else {
                            $db->query("ROLLBACK");
                            $errors[] = "Failed to delete: $username (ID: $user_id)";
                        }
                        
                    } catch (Exception $e) {
                        $db->query("ROLLBACK");
                        $errors[] = "Error deleting $username: " . $e->getMessage();
                    }
                }
                
            } catch (Exception $e) {
                $errors[] = "Error processing pattern $pattern: " . $e->getMessage();
            }
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>✅ Bulk Deletion Complete</h4>";
        echo "<p>Successfully deleted $deleted_count test users.</p>";
        echo "</div>";
        
        if (!empty($errors)) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h4>⚠️ Errors Encountered</h4>";
            echo "<ul>";
            foreach ($errors as $error) {
                echo "<li>$error</li>";
            }
            echo "</ul>";
            echo "</div>";
        }
        
        echo "<p><a href='cleanup_test_users.php'>← Refresh Page</a></p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>❌ Error!</h4>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<style>
body { 
    font-family: Arial, sans-serif; 
    margin: 20px; 
    background: #f8f9fa; 
    line-height: 1.6;
}
h1, h2, h3 { color: #333; }
h1 { border-bottom: 3px solid #007bff; padding-bottom: 10px; }
h2 { border-bottom: 1px solid #dee2e6; padding-bottom: 5px; }
table { 
    border-collapse: collapse; 
    width: 100%; 
    margin: 10px 0; 
    background: white;
}
th, td { 
    border: 1px solid #ddd; 
    padding: 8px; 
    text-align: left; 
}
th { 
    background-color: #f2f2f2; 
    font-weight: bold;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
p { 
    margin: 5px 0; 
}
</style>

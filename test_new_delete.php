<?php
/**
 * Test script for the new delete user functionality
 */

require_once 'config/config.php';

echo "<h2>🧪 Testing New Delete User Functionality</h2>";
echo "<hr>";

// Check if we have test users to work with
try {
    $db = getDB();
    
    echo "<h3>📋 Current Users in Database:</h3>";
    $users_query = "SELECT id, username, email, first_name, last_name, is_admin, status FROM accounts ORDER BY id";
    $users_result = $db->query($users_query);
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
    echo "<tr style='background: #f2f2f2;'>";
    echo "<th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>Admin</th><th>Status</th><th>Test Delete</th>";
    echo "</tr>";
    
    $test_users = [];
    while ($user = $users_result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$user['id']}</td>";
        echo "<td>{$user['username']}</td>";
        echo "<td>{$user['email']}</td>";
        echo "<td>{$user['first_name']} {$user['last_name']}</td>";
        echo "<td>" . ($user['is_admin'] ? '✅ Yes' : '❌ No') . "</td>";
        echo "<td>{$user['status']}</td>";
        
        if (!$user['is_admin']) {
            echo "<td><a href='?test_delete={$user['id']}' onclick='return confirm(\"Test delete user {$user['username']}?\")' style='color: red; text-decoration: none;'>🗑️ Test Delete</a></td>";
            $test_users[] = $user;
        } else {
            echo "<td>-</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
    if (empty($test_users)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>⚠️ No Test Users Available</h4>";
        echo "<p>Creating a test user for deletion testing...</p>";
        
        // Create a test user
        $test_username = 'delete_test_' . time();
        $test_email = $test_username . '@test.com';
        $account_number = 'ACC' . time();
        
        $create_user_sql = "INSERT INTO accounts (account_number, username, password, email, first_name, last_name, balance, is_admin) 
                           VALUES (?, ?, ?, ?, 'Test', 'User', 100.00, 0)";
        
        $hashed_password = password_hash('testpass123', PASSWORD_DEFAULT);
        $new_user_id = $db->insert($create_user_sql, [$account_number, $test_username, $hashed_password, $test_email]);
        
        echo "<p>✅ Created test user: <strong>$test_username</strong> (ID: $new_user_id)</p>";
        echo "<p><a href='?test_delete=$new_user_id' style='background: #dc3545; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🗑️ Test Delete This User</a></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>❌ Database Error</h4>";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

// Handle test deletion
if (isset($_GET['test_delete'])) {
    $user_id = (int)$_GET['test_delete'];
    
    echo "<h3>🧪 Testing Delete Process for User ID: $user_id</h3>";
    
    // Simulate admin session
    session_start();
    $_SESSION['user_id'] = 1; // Assume admin user ID is 1
    $_SESSION['is_admin'] = true;
    
    // Change to admin directory to include the delete script
    $original_dir = getcwd();
    chdir(__DIR__ . '/admin');
    
    // Set up the request simulation
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_GET['id'] = $user_id;
    $_GET['ajax'] = '1';
    
    echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>📡 Simulating AJAX Delete Request</h4>";
    echo "<p><strong>Request Method:</strong> POST</p>";
    echo "<p><strong>User ID:</strong> $user_id</p>";
    echo "<p><strong>AJAX Flag:</strong> 1</p>";
    echo "</div>";
    
    // Capture the output
    ob_start();
    
    try {
        include 'delete-user-new.php';
        $response = ob_get_contents();
    } catch (Exception $e) {
        $response = json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
    }
    
    ob_end_clean();
    
    // Change back to original directory
    chdir($original_dir);
    
    echo "<h4>📥 Response from delete-user-new.php:</h4>";
    echo "<pre style='background: #f8f9fa; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6; overflow: auto;'>";
    echo htmlspecialchars($response);
    echo "</pre>";
    
    // Try to parse the JSON response
    $json_response = json_decode($response, true);
    if ($json_response) {
        echo "<h4>📊 Parsed Response:</h4>";
        echo "<div style='background: " . ($json_response['success'] ? '#d4edda' : '#f8d7da') . "; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<p><strong>Success:</strong> " . ($json_response['success'] ? '✅ Yes' : '❌ No') . "</p>";
        echo "<p><strong>Message:</strong> " . htmlspecialchars($json_response['message']) . "</p>";
        
        if (isset($json_response['data'])) {
            echo "<p><strong>Additional Data:</strong></p>";
            echo "<pre>" . htmlspecialchars(json_encode($json_response['data'], JSON_PRETTY_PRINT)) . "</pre>";
        }
        
        if (isset($json_response['debug_error'])) {
            echo "<p><strong>Debug Error:</strong> " . htmlspecialchars($json_response['debug_error']) . "</p>";
        }
        echo "</div>";
        
        if ($json_response['success']) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
            echo "<h4>🎉 Delete Test Successful!</h4>";
            echo "<p>The user has been successfully deleted. Refresh the page to see the updated user list.</p>";
            echo "<p><a href='test_new_delete.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>🔄 Refresh Page</a></p>";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
        echo "<h4>❌ Invalid Response</h4>";
        echo "<p>The response is not valid JSON. This might indicate an error in the delete script.</p>";
        echo "</div>";
    }
}

echo "<hr>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
echo "<h4>🔍 How to Use This Test</h4>";
echo "<ol>";
echo "<li>Review the current users in the table above</li>";
echo "<li>Click 'Test Delete' next to any non-admin user</li>";
echo "<li>The script will simulate the AJAX delete request</li>";
echo "<li>Review the response to ensure it works correctly</li>";
echo "<li>Check that the user is actually removed from the database</li>";
echo "</ol>";
echo "</div>";
?>

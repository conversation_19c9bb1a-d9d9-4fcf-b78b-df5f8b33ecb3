<?php
/**
 * Secure User Deletion/Deactivation System
 * Implements safer user management with multiple deletion options
 */

require_once '../config/config.php';

// Ensure admin access
requireAdmin();

/**
 * Send JSON response and exit
 */
function sendJsonResponse($success, $message, $data = []) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

/**
 * Get user information with related data summary
 */
function getUserWithRelatedData($user_id) {
    try {
        $db = getDB();
        
        // Get basic user info
        $sql = "SELECT id, username, email, first_name, last_name, is_admin, status, balance, created_at 
                FROM accounts WHERE id = ?";
        $stmt = $db->getConnection()->prepare($sql);
        $stmt->bind_param('i', $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if (!$result || $result->num_rows === 0) {
            return null;
        }
        
        $user = $result->fetch_assoc();
        
        // Get related data counts
        $related_data = [];
        
        // Transfers - check if table exists first
        try {
            $sql = "SELECT COUNT(*) as count, COALESCE(SUM(amount), 0) as total_amount 
                    FROM transfers WHERE sender_id = ? OR recipient_id = ?";
            $stmt = $db->getConnection()->prepare($sql);
            $stmt->bind_param('ii', $user_id, $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $transfers = $result->fetch_assoc();
            $related_data['transfers'] = [
                'count' => $transfers['count'],
                'total_amount' => $transfers['total_amount']
            ];
        } catch (Exception $e) {
            // Transfers table might not exist
            $related_data['transfers'] = ['count' => 0, 'total_amount' => 0];
        }
        
        // Tickets - check if table exists first
        try {
            $sql = "SELECT COUNT(*) as count FROM tickets WHERE user_id = ?";
            $stmt = $db->getConnection()->prepare($sql);
            $stmt->bind_param('i', $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $related_data['tickets'] = $result->fetch_assoc()['count'];
        } catch (Exception $e) {
            // Tickets table might not exist
            $related_data['tickets'] = 0;
        }
        
        // Check for recent activity (last 30 days) - only if transfers table exists
        try {
            $sql = "SELECT COUNT(*) as count FROM transfers 
                    WHERE (sender_id = ? OR recipient_id = ?) 
                    AND transfer_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            $stmt = $db->getConnection()->prepare($sql);
            $stmt->bind_param('ii', $user_id, $user_id);
            $stmt->execute();
            $result = $stmt->get_result();
            $related_data['recent_activity'] = $result->fetch_assoc()['count'];
        } catch (Exception $e) {
            $related_data['recent_activity'] = 0;
        }
        
        $user['related_data'] = $related_data;
        
        return $user;
        
    } catch (Exception $e) {
        error_log("Error fetching user data: " . $e->getMessage());
        return null;
    }
}

/**
 * Soft delete user - disable account but preserve all data
 */
function softDeleteUser($user_id, $reason = '') {
    try {
        $db = getDB();
        
        $db->beginTransaction();
        
        // Update user status to deleted
        $sql = "UPDATE accounts SET 
                status = 'deleted',
                deleted_at = NOW(),
                deleted_by = ?,
                deletion_reason = ?
                WHERE id = ?";
        
        $result = $db->query($sql, [$_SESSION['user_id'], $reason, $user_id]);
        
        if ($db->getConnection()->affected_rows === 0) {
            throw new Exception("Failed to soft delete user");
        }
        
        // Log the soft deletion
        logActivity(
            $_SESSION['user_id'],
            'USER_SOFT_DELETED',
            'accounts',
            $user_id,
            [],
            ['reason' => $reason, 'deleted_by' => $_SESSION['username']]
        );
        
        $db->commit();
        
        return ['success' => true, 'message' => 'User account has been disabled and marked as deleted'];
        
    } catch (Exception $e) {
        $db->rollback();
        error_log("Error in soft delete: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to disable user account'];
    }
}

/**
 * Archive user - move to archive table and disable
 */
function archiveUser($user_id, $reason = '') {
    try {
        $db = getDB();
        
        $db->beginTransaction();
        
        // First, get all user data
        $user_data = getUserWithRelatedData($user_id);
        if (!$user_data) {
            throw new Exception("User not found");
        }
        
        // Create archive entry
        $sql = "INSERT INTO user_archive (
                    original_user_id, username, email, first_name, last_name,
                    balance, status, created_at, archived_at, archived_by, archive_reason,
                    related_data_summary
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?, ?, ?)";
        
        $result = $db->query($sql, [
            $user_id,
            $user_data['username'],
            $user_data['email'],
            $user_data['first_name'],
            $user_data['last_name'],
            $user_data['balance'],
            $user_data['status'],
            $user_data['created_at'],
            $_SESSION['user_id'],
            $reason,
            json_encode($user_data['related_data'])
        ]);
        
        // Soft delete the user
        $soft_result = softDeleteUser($user_id, "Archived: " . $reason);
        if (!$soft_result['success']) {
            throw new Exception($soft_result['message']);
        }
        
        $db->commit();
        
        return ['success' => true, 'message' => 'User has been archived and account disabled'];
        
    } catch (Exception $e) {
        $db->rollback();
        error_log("Error in archive user: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to archive user: ' . $e->getMessage()];
    }
}

/**
 * Hard delete user - only for special cases with additional verification
 */
function hardDeleteUser($user_id, $confirmation_code, $reason = '') {
    try {
        // Verify confirmation code
        if (!verifyDeletionCode($confirmation_code, $user_id)) {
            return ['success' => false, 'message' => 'Invalid confirmation code'];
        }
        
        // Include the original delete functionality but with extra logging
        require_once 'delete-user.php';
        
        // Log the hard deletion attempt
        logActivity(
            $_SESSION['user_id'],
            'USER_HARD_DELETE_ATTEMPTED',
            'accounts',
            $user_id,
            [],
            ['reason' => $reason, 'confirmation_code' => $confirmation_code]
        );
        
        return ['success' => true, 'message' => 'User and all related data permanently deleted'];
        
    } catch (Exception $e) {
        error_log("Error in hard delete: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to permanently delete user'];
    }
}

/**
 * Generate and store verification code for hard deletion
 */
function generateDeletionCode($user_id) {
    try {
        $db = getDB();
        
        $code = strtoupper(substr(md5(uniqid() . $user_id . time()), 0, 8));
        $expires_at = date('Y-m-d H:i:s', strtotime('+1 hour'));
        
        // Store the code
        $sql = "INSERT INTO deletion_codes (user_id, code, generated_by, expires_at) 
                VALUES (?, ?, ?, ?) 
                ON DUPLICATE KEY UPDATE 
                code = VALUES(code), 
                generated_by = VALUES(generated_by), 
                expires_at = VALUES(expires_at)";
        
        $db->query($sql, [$user_id, $code, $_SESSION['user_id'], $expires_at]);
        
        return $code;
        
    } catch (Exception $e) {
        error_log("Error generating deletion code: " . $e->getMessage());
        return false;
    }
}

/**
 * Verify deletion confirmation code
 */
function verifyDeletionCode($code, $user_id) {
    try {
        $db = getDB();
        
        $sql = "SELECT id FROM deletion_codes 
                WHERE user_id = ? AND code = ? AND expires_at > NOW() AND used_at IS NULL";
        $result = $db->query($sql, [$user_id, $code]);
        
        if ($result && $result->num_rows > 0) {
            // Mark code as used
            $db->query("UPDATE deletion_codes SET used_at = NOW() WHERE user_id = ? AND code = ?", [$user_id, $code]);
            return true;
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("Error verifying deletion code: " . $e->getMessage());
        return false;
    }
}

/**
 * Security validation for deletion operations
 */
function validateDeletionRequest($user, $action) {
    $errors = [];
    
    if (!$user) {
        $errors[] = "User not found";
        return $errors;
    }
    
    // Prevent admin deletion
    if ($user['is_admin']) {
        $errors[] = "Administrator accounts cannot be deleted";
    }
    
    // Prevent self-deletion
    if ($user['id'] == $_SESSION['user_id']) {
        $errors[] = "You cannot delete your own account";
    }
    
    // Admin has full control - no balance or activity restrictions
    // All validation checks removed for admin convenience
    
    return $errors;
}

// Main logic
try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendJsonResponse(false, 'Invalid request method');
    }
    
    $user_id = intval($_POST['id'] ?? 0);
    $action = $_POST['action'] ?? '';
    $reason = trim($_POST['reason'] ?? '');
    $confirmation_code = $_POST['confirmation_code'] ?? '';
    
    if ($user_id <= 0) {
        sendJsonResponse(false, 'Invalid user ID');
    }
    
    // Handle get_user_data separately (no validation needed)
    if ($action === 'get_user_data') {
        $user = getUserWithRelatedData($user_id);
        if ($user) {
            sendJsonResponse(true, 'User data retrieved', $user);
        } else {
            sendJsonResponse(false, 'User not found');
        }
    }
    
    // For other actions, get user data and validate
    $user = getUserWithRelatedData($user_id);
    
    // Validate request
    $errors = validateDeletionRequest($user, $action);
    if (!empty($errors)) {
        sendJsonResponse(false, implode('; ', $errors));
    }
    
    switch ($action) {
            
        case 'soft_delete':
            $result = softDeleteUser($user_id, $reason);
            sendJsonResponse($result['success'], $result['message']);
            break;
            
        case 'archive':
            $result = archiveUser($user_id, $reason);
            sendJsonResponse($result['success'], $result['message']);
            break;
            
        case 'hard_delete':
            if (empty($confirmation_code)) {
                sendJsonResponse(false, 'Confirmation code required for permanent deletion');
            }
            $result = hardDeleteUser($user_id, $confirmation_code, $reason);
            sendJsonResponse($result['success'], $result['message']);
            break;
            
        case 'generate_code':
            $code = generateDeletionCode($user_id);
            if ($code) {
                sendJsonResponse(true, 'Confirmation code generated', ['code' => $code]);
            } else {
                sendJsonResponse(false, 'Failed to generate confirmation code');
            }
            break;
            
        default:
            sendJsonResponse(false, 'Invalid action specified');
    }
    
} catch (Exception $e) {
    error_log("Error in secure-delete-user.php: " . $e->getMessage());
    sendJsonResponse(false, 'An unexpected error occurred');
}
?>

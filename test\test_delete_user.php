<?php
require_once '../config/config.php';

echo "<h2>Testing User Deletion</h2>";

if (isset($_GET['delete_id'])) {
    $user_id = (int)$_GET['delete_id'];
    
    echo "<h3>Attempting to delete user ID: $user_id</h3>";
    
    try {
        $db = getDB();
        
        // First, get user details
        $user_query = "SELECT id, username, email, first_name, last_name, account_number FROM accounts WHERE id = ? AND is_admin = 0";
        $user_result = $db->query($user_query, [$user_id]);
        
        if ($user_result->num_rows === 0) {
            echo "<p style='color: red;'>❌ User not found or is admin user.</p>";
            exit;
        }
        
        $user = $user_result->fetch_assoc();
        echo "<p><strong>User to delete:</strong></p>";
        echo "<pre>";
        print_r($user);
        echo "</pre>";
        
        // Start transaction
        echo "<p>Starting transaction...</p>";
        $db->query("START TRANSACTION");
        
        try {
            // Delete OTP records (only if table exists)
            echo "<p>Deleting OTP records...</p>";
            try {
                $affected = $db->delete("DELETE FROM user_otps WHERE user_id = ?", [$user_id]);
                echo "✅ OTP records deleted (affected rows: " . $affected . ")<br>";
            } catch (Exception $e) {
                echo "⚠️ OTP table issue: " . $e->getMessage() . "<br>";
            }

            // Delete transaction history (only if table exists)
            echo "<p>Deleting transaction history...</p>";
            try {
                $affected = $db->delete("DELETE FROM transactions WHERE account_id = ?", [$user_id]);
                echo "✅ Transaction records deleted (affected rows: " . $affected . ")<br>";
            } catch (Exception $e) {
                echo "⚠️ Transactions table issue: " . $e->getMessage() . "<br>";
            }

            // Delete virtual cards (only if table exists)
            echo "<p>Deleting virtual cards...</p>";
            try {
                $affected = $db->delete("DELETE FROM virtual_cards WHERE user_id = ?", [$user_id]);
                echo "✅ Virtual cards deleted (affected rows: " . $affected . ")<br>";
            } catch (Exception $e) {
                echo "⚠️ Virtual cards table issue: " . $e->getMessage() . "<br>";
            }

            // Delete user sessions (only if table exists)
            echo "<p>Deleting user sessions...</p>";
            try {
                $affected = $db->delete("DELETE FROM user_sessions WHERE user_id = ?", [$user_id]);
                echo "✅ User sessions deleted (affected rows: " . $affected . ")<br>";
            } catch (Exception $e) {
                echo "⚠️ User sessions table issue: " . $e->getMessage() . "<br>";
            }
            
            // Finally, delete the user account
            echo "<p>Deleting user account...</p>";
            $affected_rows = $db->delete("DELETE FROM accounts WHERE id = ? AND is_admin = 0", [$user_id]);

            if ($affected_rows > 0) {
                // Commit transaction
                $db->query("COMMIT");
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<h4>✅ Success!</h4>";
                echo "<p>User '{$user['username']}' has been permanently deleted.</p>";
                echo "<p>Affected rows: " . $affected_rows . "</p>";
                echo "</div>";
            } else {
                // Rollback transaction
                $db->query("ROLLBACK");
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
                echo "<h4>❌ Failed!</h4>";
                echo "<p>Failed to delete user. User may not exist or affected rows: " . $affected_rows . "</p>";
                echo "</div>";
            }
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $db->query("ROLLBACK");
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h4>❌ Error!</h4>";
            echo "<p>Error during deletion: " . $e->getMessage() . "</p>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h4>❌ Database Error!</h4>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    echo "<hr>";
}

// Show current users
echo "<h3>Current Users in Database:</h3>";
try {
    $db = getDB();
    $result = $db->query("SELECT id, username, email, first_name, last_name, is_admin FROM accounts ORDER BY id");
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Username</th><th>Email</th><th>Name</th><th>Is Admin</th><th>Action</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td>{$row['username']}</td>";
        echo "<td>{$row['email']}</td>";
        echo "<td>{$row['first_name']} {$row['last_name']}</td>";
        echo "<td>" . ($row['is_admin'] ? 'Yes' : 'No') . "</td>";
        if (!$row['is_admin']) {
            echo "<td><a href='?delete_id={$row['id']}' onclick='return confirm(\"Delete user {$row['username']}?\")' style='color: red;'>Delete</a></td>";
        } else {
            echo "<td>-</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
h2, h3, h4 { color: #333; }
pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
</style>

<?php
/**
 * PHPMailer Exception class
 */

namespace PHPMailer\PHPMailer;

/**
 * PHPMailer exception handler
 */
class Exception extends \Exception
{
    /**
     * Prettify error message output
     * @param string $message
     * @param integer $code
     * @param Exception $previous
     */
    public function __construct($message = '', $code = 0, Exception $previous = null)
    {
        parent::__construct($message, $code, $previous);
    }

    /**
     * Return error message as string
     * @return string
     */
    public function errorMessage()
    {
        return '<strong>' . htmlspecialchars($this->getMessage()) . "</strong><br />\n";
    }
}
?>

<?php
require_once 'config/config.php';
requireAdmin();

$page_title = 'OTP Admin Guide';
$bank_name = getBankName();
?>
<!DOCTYPE html>
<html>
<head>
    <title><?php echo $page_title . ' - ' . $bank_name; ?></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #dc3545, #c82333); color: white; padding: 20px; margin: -30px -30px 30px -30px; border-radius: 10px 10px 0 0; }
        .step { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc3545; }
        .step h3 { margin-top: 0; color: #dc3545; }
        .screenshot { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 10px 0; font-family: monospace; }
        .button { background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 10px 10px 0; }
        .button:hover { background: #c82333; color: white; }
        .button.secondary { background: #6c757d; }
        .button.secondary:hover { background: #5a6268; }
        .alert { padding: 15px; border-radius: 5px; margin: 15px 0; }
        .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .highlight { background: #fff3cd; padding: 2px 6px; border-radius: 3px; }
        ul { padding-left: 20px; }
        li { margin: 8px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 OTP Admin Guide</h1>
            <p>How to find and use OTP codes for user support</p>
        </div>
        
        <div class="alert alert-info">
            <strong>Purpose:</strong> This guide shows you where to find OTP codes in the admin panel to help users who can't receive their verification emails.
        </div>
        
        <div class="step">
            <h3>📋 Step 1: View All Users with OTP Codes</h3>
            <p>Go to the main user management page to see all users and their current OTP codes:</p>
            <ul>
                <li>Navigate to: <strong>Admin → Users → All Users</strong></li>
                <li>Or visit: <a href="admin/users.php" target="_blank">admin/users.php</a></li>
                <li>Look for the <span class="highlight">"OTP"</span> column in the users table</li>
            </ul>
            
            <div class="screenshot">
                Table columns: User | Account | Contact | Balance | Status | KYC | <strong>OTP</strong> | Joined | Actions
            </div>
            
            <p><strong>What you'll see in the OTP column:</strong></p>
            <ul>
                <li><span class="highlight">6-digit code + time</span> - Active OTP with expiration time</li>
                <li><span class="highlight">-</span> - No active OTP for this user</li>
            </ul>
        </div>
        
        <div class="step">
            <h3>👤 Step 2: View Detailed OTP Information</h3>
            <p>For more detailed OTP information about a specific user:</p>
            <ul>
                <li>Click the <strong>"Actions"</strong> dropdown next to any user</li>
                <li>Select <strong>"View Details"</strong></li>
                <li>Scroll down to the <span class="highlight">"Two-Factor Authentication (OTP)"</span> section</li>
            </ul>
            
            <p><strong>Detailed view shows:</strong></p>
            <ul>
                <li>Current OTP code (if active)</li>
                <li>Exact expiration date and time</li>
                <li>Support instructions for helping users</li>
            </ul>
        </div>
        
        <div class="step">
            <h3>🆘 Step 3: Help Users Who Can't Receive OTP</h3>
            <p>When a user contacts support saying they can't receive their OTP email:</p>
            
            <ol>
                <li><strong>Find the user</strong> in the admin users list</li>
                <li><strong>Check the OTP column</strong> - if there's a code, it's valid for 10 minutes</li>
                <li><strong>Provide the code</strong> to the user over the phone or secure channel</li>
                <li><strong>If no code exists</strong>, ask the user to try logging in again to generate a new one</li>
            </ol>
            
            <div class="alert alert-success">
                <strong>Security Note:</strong> Only provide OTP codes after verifying the user's identity through standard security questions or procedures.
            </div>
        </div>
        
        <div class="step">
            <h3>🔧 Troubleshooting Action Buttons</h3>
            <p>If the "Actions" dropdown buttons aren't working:</p>
            <ul>
                <li>Refresh the page (F5)</li>
                <li>Clear browser cache</li>
                <li>Try a different browser</li>
                <li>Check if JavaScript is enabled</li>
            </ul>
            
            <p><strong>Alternative ways to access user details:</strong></p>
            <ul>
                <li>Direct URL: <code>admin/view-user.php?id=[USER_ID]</code></li>
                <li>Edit URL: <code>admin/edit-user.php?id=[USER_ID]</code></li>
            </ul>
        </div>
        
        <div class="step">
            <h3>📊 Quick Reference</h3>
            <div class="screenshot">
<strong>OTP Status Meanings:</strong>
• <span style="background: #0d6efd; color: white; padding: 2px 6px; border-radius: 3px;">123456</span> 14:30 = Active OTP expires at 14:30
• <span style="color: #6c757d;">-</span> = No active OTP
• Empty = User hasn't attempted login recently
            </div>
        </div>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="admin/users.php" class="button">👥 View All Users</a>
            <a href="admin/" class="button secondary">🏠 Admin Dashboard</a>
            <a href="create_test_user.php" class="button secondary">🧪 Create Test User</a>
        </div>
        
        <div class="alert alert-info" style="margin-top: 30px;">
            <strong>Need Help?</strong> If you're still having trouble finding OTP codes or the action buttons aren't working, contact your system administrator or check the browser console for JavaScript errors.
        </div>
    </div>
</body>
</html>
